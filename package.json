{"name": "printphoto", "version": "1.0.0", "description": "图片处理和打印应用", "main": "main.js", "scripts": {"start": "electron .", "build": "electron-builder", "build:win": "electron-builder --win", "dist": "electron-builder --publish=never"}, "keywords": ["electron", "print", "photo", "image"], "author": "Your Name", "license": "MIT", "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1"}, "dependencies": {"ali-oss": "^6.18.1", "fabric": "^5.3.0", "qrcode": "^1.5.3"}, "build": {"appId": "com.example.printphoto", "productName": "PrintPhoto", "directories": {"output": "dist"}, "files": ["**/*", "!node_modules", "!dist"], "win": {"target": "nsis", "icon": "icon.ico"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}