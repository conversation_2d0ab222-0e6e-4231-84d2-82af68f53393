# 图片处理与打印工具

这是一个基于Electron的桌面应用程序，用于图片处理、添加文字、上传到阿里云OSS、生成二维码以及打印图片功能。

## 功能特点

- 🖼️ 选择本地图片
- ✏️ 给图片添加自定义文字
- ☁️ 上传图片到阿里云OSS
- 📱 生成二维码并添加到图片上
- 🖨️ 打印图片
- 💾 保存处理后的图片

## 安装与运行

### 环境要求

- Node.js 14.x 或更高版本
- npm 6.x 或更高版本（或pnpm/yarn）

### 安装步骤

1. 克隆或下载此仓库
   ```
   git clone https://github.com/yourusername/printphoto.git
   cd printphoto
   ```

2. 安装依赖
   ```
   pnpm install
   ```
   或者
   ```
   npm install
   ```

3. 运行应用
   ```
   pnpm start
   ```
   或者
   ```
   npm start
   ```

### 打包应用

为Windows创建可执行文件：
```
pnpm build
```
或者
```
npm run build
```

## 使用说明

1. 启动应用后，点击"选择图片"按钮选择要处理的本地图片
2. 使用文字工具可以在图片上添加自定义文字
3. 完成编辑后，可以上传到阿里云OSS（需要配置OSS账户信息）
4. 上传成功后，可以生成包含图片URL的二维码
5. 最后可以打印或保存处理好的图片

## 阿里云OSS配置

在使用上传功能前，需要在renderer.js文件中配置阿里云OSS的相关信息：

```javascript
const client = new OSS({
  region: 'oss-cn-hangzhou',      // 替换为您的OSS区域
  accessKeyId: 'YOUR_ACCESS_KEY',  // 替换为您的AccessKey ID
  accessKeySecret: 'YOUR_SECRET',  // 替换为您的AccessKey Secret
  bucket: 'YOUR_BUCKET_NAME'       // 替换为您的Bucket名称
});
```

## 技术栈

- Electron: 跨平台桌面应用框架
- Fabric.js: 强大的Canvas操作库
- QRCode.js: 二维码生成库
- Ali-OSS SDK: 阿里云OSS操作库

## 许可证

ISC 