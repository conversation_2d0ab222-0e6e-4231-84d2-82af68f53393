* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

.container {
  display: flex;
  height: 100vh;
}

.sidebar {
  width: 300px;
  background-color: #fff;
  padding: 20px;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.editor {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #e9e9e9;
  padding: 20px;
}

.canvas-container {
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

h2 {
  margin-bottom: 20px;
  color: #333;
  text-align: center;
}

h3 {
  margin: 15px 0 10px;
  font-size: 16px;
  color: #444;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

button {
  padding: 10px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #40a9ff;
}

textarea, input[type="text"] {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

textarea {
  height: 80px;
  resize: vertical;
}

.text-options {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

select, input[type="number"] {
  padding: 6px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
}

#oss-result {
  margin-top: 10px;
  padding: 10px;
  background-color: #f7f7f7;
  border-radius: 4px;
  min-height: 40px;
  word-break: break-all;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
  
  .sidebar {
    width: 100%;
    height: 40%;
    overflow-y: auto;
  }
  
  .editor {
    height: 60%;
  }
} 