# 📝 Go+Wails 项目运行指南

## ✅ 正确的运行命令

```bash
~/go/bin/wails dev
```

## 📋 详细信息

- **主要技术栈**：Go + Wails v2 框架
- **开发运行**：使用 `wails dev` 开发模式
- **DevServer端口**：http://localhost:34115
- **项目重点**：**以Go为主，Electron版本已废弃**

## 🔧 调试说明

如果遇到问题，请在浏览器中按F12打开开发者工具，查看Console标签页的调试信息：
- 上传结果的详细信息
- 二维码生成过程
- 任何错误信息

## 🚫 不要使用的命令

- ~~`npm start`~~ (Electron版本，已废弃)
- ~~`go run .`~~ (需要wails框架)
- ~~`node`相关命令~~ (Electron相关，不再使用)

## 🔧 当前状态

- ✅ Wails CLI已安装：`/Users/<USER>/go/bin/wails`
- ✅ 应用已成功启动在 http://localhost:34115
- ✅ 支持热重载开发模式
- ✅ 绑定了Go后端方法到前端JavaScript
- ✅ 阿里云OSS真实上传功能已实现

## 📁 项目结构

- `app.go` - Go后端逻辑
- `main.go` - 应用入口
- `frontend/` - 前端资源
- `wails.json` - Wails配置

## 🎯 记住

**下次直接用 `~/go/bin/wails dev` 启动Go版本！**

## 📱 功能特性

- 多图片选择和缩略图管理
- Fabric.js画布编辑和文字添加
- 阿里云OSS自动上传和批量操作
- 二维码生成（文本和图片URL）
- 本地打印和保存功能
- 上传进度显示
- 完整链接显示和复制功能
- 每个链接独立生成二维码

## 🛠️ 开发说明

1. 修改后端代码：编辑 `app.go`
2. 修改前端代码：编辑 `frontend/` 目录下的文件
3. 热重载：保存文件后自动重新编译和刷新
4. 访问：浏览器打开 http://localhost:34115
5. 调试：按F12查看控制台信息

---
*最后更新：2025年6月2日* 