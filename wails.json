{"$schema": "https://wails.io/schemas/config.v2.json", "name": "printphoto", "outputfilename": "printphoto", "frontend": {"dir": "./frontend", "install": "", "build": "", "dev": "", "package": "", "watcher": {"ignore": [], "reload": true}}, "author": {"name": "", "email": ""}, "info": {"companyName": "", "productName": "PrintPhoto", "productVersion": "1.0.0", "copyright": "", "comments": "图片处理和打印应用"}, "nsisType": "multiple", "obfuscated": false, "garbleargs": "", "windowStartState": "Normal", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "disableResize": false, "fullscreenOnStart": false, "alwaysOnTop": false, "backgroundColor": "#ffffff"}