package main

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"image"
	"image/draw"
	"image/jpeg"
	"image/png"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/skip2/go-qrcode"
	"golang.org/x/image/font"
	"golang.org/x/image/font/basicfont"
	"golang.org/x/image/math/fixed"
)

// App struct
type App struct {
	ctx       context.Context
	ossClient *oss.Client
}

// ImageInfo 图片信息
type ImageInfo struct {
	Path     string `json:"path"`
	Name     string `json:"name"`
	Selected bool   `json:"selected"`
}

// UploadResult 上传结果
type UploadResult struct {
	Name string `json:"name"`
	URL  string `json:"url"`
}

// NewApp 创建新的应用实例
func NewApp() *App {
	return &App{}
}

// startup 应用启动时调用
func (a *App) startup(ctx context.Context) {
	a.ctx = ctx
	// 初始化OSS客户端
	a.initOSSClient()
}

// initOSSClient 初始化阿里云OSS客户端
func (a *App) initOSSClient() {
	client, err := oss.New(
		"https://oss-cn-qingdao.aliyuncs.com",
		"LTAI5tAgxEfwZrWw1sSpm9qF",
		"******************************",
	)
	if err != nil {
		log.Printf("初始化OSS客户端失败: %v", err)
		return
	}
	a.ossClient = client
}

// SelectImages 选择图片文件
func (a *App) SelectImages() ([]ImageInfo, error) {
	// 在Wails中，文件选择需要通过前端JavaScript实现
	// 这里返回空数组，实际实现在前端
	return []ImageInfo{}, nil
}

// ProcessImageWithText 在图片上添加文字
func (a *App) ProcessImageWithText(imagePath, text string, x, y int) (string, error) {
	// 读取图片
	file, err := os.Open(imagePath)
	if err != nil {
		return "", fmt.Errorf("打开图片失败: %v", err)
	}
	defer file.Close()

	// 解码图片
	img, format, err := image.Decode(file)
	if err != nil {
		return "", fmt.Errorf("解码图片失败: %v", err)
	}

	// 创建新的图片用于绘制
	bounds := img.Bounds()
	rgba := image.NewRGBA(bounds)
	draw.Draw(rgba, bounds, img, bounds.Min, draw.Src)

	// 添加文字
	col := image.Black
	point := fixed.Point26_6{
		X: fixed.Int26_6(x * 64),
		Y: fixed.Int26_6(y * 64),
	}

	d := &font.Drawer{
		Dst:  rgba,
		Src:  col,
		Face: basicfont.Face7x13,
		Dot:  point,
	}
	d.DrawString(text)

	// 保存处理后的图片
	outputPath := filepath.Join(os.TempDir(), fmt.Sprintf("processed_%d.%s", time.Now().Unix(), format))
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return "", fmt.Errorf("创建输出文件失败: %v", err)
	}
	defer outputFile.Close()

	// 根据格式编码图片
	switch format {
	case "jpeg":
		// 使用高质量JPEG编码 (95%质量)
		err = jpeg.Encode(outputFile, rgba, &jpeg.Options{Quality: 95})
	case "png":
		err = png.Encode(outputFile, rgba)
	default:
		err = png.Encode(outputFile, rgba)
	}

	if err != nil {
		return "", fmt.Errorf("编码图片失败: %v", err)
	}

	return outputPath, nil
}

// GenerateQRCode 生成二维码
func (a *App) GenerateQRCode(content string) (string, error) {
	log.Printf("📍 GenerateQRCode 开始处理，输入内容: \"%s\"", content)
	log.Printf("📍 输入内容长度: %d", len(content))

	if content == "" {
		log.Printf("📍 GenerateQRCode 错误: 二维码内容不能为空")
		return "", fmt.Errorf("二维码内容不能为空")
	}

	// 生成二维码
	log.Printf("📍 开始生成二维码...")
	qrCode, err := qrcode.Encode(content, qrcode.Medium, 256)
	if err != nil {
		log.Printf("📍 GenerateQRCode 错误: 生成二维码失败: %v", err)
		return "", fmt.Errorf("生成二维码失败: %v", err)
	}
	log.Printf("📍 二维码生成成功，原始数据长度: %d", len(qrCode))

	// 转换为base64
	base64Str := base64.StdEncoding.EncodeToString(qrCode)
	result := "data:image/png;base64," + base64Str

	log.Printf("📍 base64转换完成，最终结果长度: %d", len(result))
	log.Printf("📍 最终结果前50字符: %s", result[:min(50, len(result))])
	log.Printf("📍 最终结果后50字符: %s", result[max(0, len(result)-50):])
	log.Printf("📍 GenerateQRCode 完成")

	return result, nil
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

// UploadToOSS 上传文件到阿里云OSS
func (a *App) UploadToOSS(filePath string) (*UploadResult, error) {
	if a.ossClient == nil {
		return nil, fmt.Errorf("OSS客户端未初始化")
	}

	// 获取bucket
	bucket, err := a.ossClient.Bucket("moneyyy")
	if err != nil {
		return nil, fmt.Errorf("获取bucket失败: %v", err)
	}

	// 读取文件
	data, err := ioutil.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("读取文件失败: %v", err)
	}

	// 生成对象名
	fileName := filepath.Base(filePath)
	objectName := fmt.Sprintf("printphoto/%d_%s", time.Now().Unix(), fileName)

	// 上传文件
	err = bucket.PutObject(objectName, bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("上传文件失败: %v", err)
	}

	// 生成访问URL
	url := fmt.Sprintf("https://moneyyy.oss-cn-qingdao.aliyuncs.com/%s", objectName)

	return &UploadResult{
		Name: fileName,
		URL:  url,
	}, nil
}

// UploadImageData 上传图片数据到OSS
func (a *App) UploadImageData(imageData string, filename string) (*UploadResult, error) {
	if a.ossClient == nil {
		return nil, fmt.Errorf("OSS客户端未初始化")
	}

	// 解析base64图片数据
	if !strings.HasPrefix(imageData, "data:image/") {
		return nil, fmt.Errorf("无效的图片数据格式")
	}

	// 提取base64数据
	parts := strings.Split(imageData, ",")
	if len(parts) != 2 {
		return nil, fmt.Errorf("无效的base64数据格式")
	}

	data, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return nil, fmt.Errorf("解码base64数据失败: %v", err)
	}

	// 获取bucket
	bucket, err := a.ossClient.Bucket("moneyyy")
	if err != nil {
		return nil, fmt.Errorf("获取bucket失败: %v", err)
	}

	// 如果没有提供文件名，使用默认名
	if filename == "" {
		filename = "canvas.png"
	}

	// 生成对象名，使用原始文件名和时间戳避免冲突
	ext := filepath.Ext(filename)
	nameWithoutExt := strings.TrimSuffix(filename, ext)
	objectName := fmt.Sprintf("printphoto/%s_%d%s", nameWithoutExt, time.Now().Unix(), ext)

	// 上传数据
	err = bucket.PutObject(objectName, bytes.NewReader(data))
	if err != nil {
		return nil, fmt.Errorf("上传数据失败: %v", err)
	}

	// 生成访问URL
	url := fmt.Sprintf("https://moneyyy.oss-cn-qingdao.aliyuncs.com/%s", objectName)

	return &UploadResult{
		Name: filename,
		URL:  url,
	}, nil
}

// BatchUploadImages 批量上传图片
func (a *App) BatchUploadImages(imagePaths []string) ([]*UploadResult, error) {
	if a.ossClient == nil {
		return nil, fmt.Errorf("OSS客户端未初始化")
	}

	var results []*UploadResult
	for _, path := range imagePaths {
		result, err := a.UploadToOSS(path)
		if err != nil {
			log.Printf("上传文件 %s 失败: %v", path, err)
			continue
		}
		results = append(results, result)
	}

	return results, nil
}

// SaveImageData 保存图片数据到本地
func (a *App) SaveImageData(imageData, filename string) (string, error) {
	// 解析base64图片数据
	if !strings.HasPrefix(imageData, "data:image/") {
		return "", fmt.Errorf("无效的图片数据格式")
	}

	// 提取base64数据
	parts := strings.Split(imageData, ",")
	if len(parts) != 2 {
		return "", fmt.Errorf("无效的base64数据格式")
	}

	data, err := base64.StdEncoding.DecodeString(parts[1])
	if err != nil {
		return "", fmt.Errorf("解码base64数据失败: %v", err)
	}

	// 获取用户主目录
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return "", fmt.Errorf("获取用户主目录失败: %v", err)
	}

	// 创建保存路径
	savePath := filepath.Join(homeDir, "Downloads", filename)

	// 写入文件
	err = ioutil.WriteFile(savePath, data, 0644)
	if err != nil {
		return "", fmt.Errorf("保存文件失败: %v", err)
	}

	return savePath, nil
}

// GetImageInfo 获取图片信息
func (a *App) GetImageInfo(imagePath string) (*ImageInfo, error) {
	info, err := os.Stat(imagePath)
	if err != nil {
		return nil, fmt.Errorf("获取文件信息失败: %v", err)
	}

	return &ImageInfo{
		Path:     imagePath,
		Name:     info.Name(),
		Selected: false,
	}, nil
}
