# PrintPhoto 项目构建文档

## 项目简介
这是一个基于 Wails v2 的图片处理应用程序，支持图片处理、二维码生成和阿里云OSS上传功能。

## 环境要求
- Go 1.19+ (当前测试版本: Go 1.24.0)
- Wails v2.10.1
- Windows 系统

## 安装步骤

### 1. 检查Go环境
```cmd
go version
```
应该显示: `go version go1.24.0 windows/amd64`

### 2. 安装Wails
```cmd
go install github.com/wailsapp/wails/v2/cmd/wails@latest
```

### 3. 检查安装路径
检查你的GOPATH:
```cmd
go env GOPATH
```
示例输出: `E:\yy\goprojects`

Wails会安装到: `%GOPATH%\bin\wails.exe`

### 4. 验证Wails安装
```cmd
E:\yy\goprojects\bin\wails.exe version
```
应该显示: `v2.10.1`

## 开发运行

### 设置Go代理（解决网络问题）
```cmd
$env:GOPROXY="https://goproxy.cn,direct"
$env:GOSUMDB="sum.golang.google.cn"
```

### 运行开发模式
```cmd
E:\yy\goprojects\bin\wails.exe dev
```

## 生产构建

### 打包exe文件
```cmd
$env:GOPROXY="https://goproxy.cn,direct"
$env:GOSUMDB="sum.golang.google.cn"
E:\yy\goprojects\bin\wails.exe build
```

### 构建输出
- 构建成功后，exe文件位于: `build\bin\printphoto.exe`
- 构建时间约: 9.276秒

## 项目结构
```
printphoto/
├── app.go              # 主应用逻辑
├── main.go             # 入口文件
├── wails.json          # Wails配置文件
├── build.sh            # Linux构建脚本
├── go.mod              # Go模块依赖
├── go.sum              # 依赖校验和
├── frontend/           # 前端资源
│   ├── index.html      # 主页面
│   ├── app.js          # 前端逻辑
│   └── wailsjs/        # Wails生成的JS绑定
└── build/              # 构建输出目录
    └── bin/
        └── printphoto.exe  # 最终可执行文件
```

## 功能特性
- 图片选择和处理
- 文字添加到图片
- 二维码生成
- 阿里云OSS文件上传
- 现代化Web UI界面

## 常见问题

### 1. wails命令找不到
确保使用完整路径: `E:\yy\goprojects\bin\wails.exe`

### 2. 网络连接问题
设置国内Go代理:
```cmd
$env:GOPROXY="https://goproxy.cn,direct"
$env:GOSUMDB="sum.golang.google.cn"
```

### 3. 依赖下载失败
检查网络连接，确保代理设置正确

## 版本信息
- Go: 1.24.0
- Wails: v2.10.1
- 平台: windows/amd64
- 构建模式: production

## 技术栈
- 后端: Go + Wails v2
- 前端: HTML + JavaScript
- 云服务: 阿里云OSS
- 图片处理: Go标准库

---
最后更新时间: 2024年 