const { ipc<PERSON><PERSON><PERSON> } = require('electron');
const OSS = require('ali-oss');
const QRCode = require('qrcode');

// 阿里云OSS配置
const ossConfig = {
    region: 'oss-cn-qingdao',
    accessKeyId: 'LTAI5tAgxEfwZrWw1sSpm9qF',
    accessKeySecret: '******************************',
    bucket: 'moneyyy'
};

// 全局变量
let canvas;
let selectedImages = [];
let currentImageIndex = 0;

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Fabric.js画布
    canvas = new fabric.Canvas('canvas');
    
    // 绑定事件
    bindEvents();
    
    // 更新界面
    updateImageCounter();
    updateImageList();
});

function bindEvents() {
    // 选择图片按钮
    document.getElementById('selectBtn').addEventListener('click', selectImages);
    
    // 添加文字按钮
    document.getElementById('addTextBtn').addEventListener('click', addText);
    
    // 生成二维码按钮
    document.getElementById('generateQRBtn').addEventListener('click', generateQRCode);
    
    // 打印按钮
    document.getElementById('printBtn').addEventListener('click', printImage);
    
    // 保存按钮
    document.getElementById('saveBtn').addEventListener('click', saveImage);
    
    // 图片列表控制按钮
    document.getElementById('selectAllBtn').addEventListener('click', selectAllImages);
    document.getElementById('deselectAllBtn').addEventListener('click', deselectAllImages);
    document.getElementById('uploadSelectedBtn').addEventListener('click', uploadSelectedImages);
    document.getElementById('clearListBtn').addEventListener('click', clearImageList);
    
    // 导航按钮
    document.getElementById('prevBtn').addEventListener('click', showPreviousImage);
    document.getElementById('nextBtn').addEventListener('click', showNextImage);
}

// 选择图片
async function selectImages() {
    try {
        const filePaths = await ipcRenderer.invoke('select-images');
        if (filePaths && filePaths.length > 0) {
            // 添加新图片到现有列表
            filePaths.forEach(path => {
                if (!selectedImages.find(img => img.path === path)) {
                    selectedImages.push({
                        path: path,
                        name: path.split('/').pop(),
                        selected: false
                    });
                }
            });
            
            // 如果这是第一次选择图片，显示第一张
            if (currentImageIndex === 0 && selectedImages.length > 0) {
                showImage(0);
            }
            
            updateImageCounter();
            updateImageList();
        }
    } catch (error) {
        console.error('选择图片失败:', error);
        alert('选择图片失败: ' + error.message);
    }
}

// 显示图片
function showImage(index) {
    if (index < 0 || index >= selectedImages.length) return;
    
    currentImageIndex = index;
    const imagePath = selectedImages[index].path;
    
    // 清空画布
    canvas.clear();
    
    // 加载图片
    fabric.Image.fromURL(`file://${imagePath}`, function(img) {
        // 计算合适的大小
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        const imgAspectRatio = img.width / img.height;
        const canvasAspectRatio = canvasWidth / canvasHeight;
        
        let scaleFactor;
        if (imgAspectRatio > canvasAspectRatio) {
            scaleFactor = canvasWidth / img.width;
        } else {
            scaleFactor = canvasHeight / img.height;
        }
        
        img.scale(scaleFactor);
        img.center();
        
        canvas.add(img);
        canvas.renderAll();
    });
    
    updateNavigationButtons();
    updateImageList();
}

// 添加文字
function addText() {
    const textContent = document.getElementById('textInput').value;
    if (!textContent) {
        alert('请输入要添加的文字');
        return;
    }
    
    const text = new fabric.Text(textContent, {
        left: 100,
        top: 100,
        fontFamily: 'Arial',
        fontSize: 30,
        fill: '#000000'
    });
    
    canvas.add(text);
    canvas.setActiveObject(text);
    canvas.renderAll();
    
    // 清空输入框
    document.getElementById('textInput').value = '';
}

// 生成二维码
async function generateQRCode() {
    try {
        let qrText = document.getElementById('qrInput').value.trim();
        
        // 如果没有输入内容，自动上传当前图片并使用URL
        if (!qrText) {
            if (selectedImages.length === 0) {
                alert('请先选择图片或输入二维码内容');
                return;
            }
            
            // 获取当前画布内容并上传
            const dataURL = canvas.toDataURL('image/png');
            const uploadedUrl = await uploadCanvasToOSS(dataURL);
            if (uploadedUrl) {
                qrText = uploadedUrl;
            } else {
                alert('自动上传图片失败，请手动输入二维码内容');
                return;
            }
        }
        
        // 生成二维码
        const qrCodeDataURL = await QRCode.toDataURL(qrText);
        
        // 显示二维码
        const qrContainer = document.getElementById('qrcode');
        qrContainer.innerHTML = `<img src="${qrCodeDataURL}" style="max-width: 100%; height: auto;" />`;
        
        // 如果是自动上传的，更新输入框显示URL
        if (document.getElementById('qrInput').value.trim() === '') {
            document.getElementById('qrInput').value = qrText;
        }
        
    } catch (error) {
        console.error('生成二维码失败:', error);
        alert('生成二维码失败: ' + error.message);
    }
}

// 上传画布内容到OSS
async function uploadCanvasToOSS(dataURL) {
    try {
        showProgress(true);
        updateProgress(0, '准备上传...');
        
        const client = new OSS(ossConfig);
        
        // 将dataURL转换为Buffer
        const base64Data = dataURL.replace(/^data:image\/\w+;base64,/, '');
        const buffer = Buffer.from(base64Data, 'base64');
        
        // 生成文件名
        const timestamp = Date.now();
        const fileName = `printphoto/canvas_${timestamp}.png`;
        
        updateProgress(50, '正在上传...');
        
        // 上传到OSS
        const result = await client.put(fileName, buffer);
        
        updateProgress(100, '上传完成');
        setTimeout(() => showProgress(false), 1000);
        
        console.log('上传成功:', result.url);
        return result.url;
        
    } catch (error) {
        console.error('上传失败:', error);
        showProgress(false);
        throw error;
    }
}

// 打印图片
async function printImage() {
    try {
        const dataURL = canvas.toDataURL('image/png');
        await ipcRenderer.invoke('print-image', dataURL);
    } catch (error) {
        console.error('打印失败:', error);
        alert('打印失败: ' + error.message);
    }
}

// 保存图片
async function saveImage() {
    try {
        const dataURL = canvas.toDataURL('image/png');
        const timestamp = Date.now();
        const defaultName = `processed_image_${timestamp}.png`;
        
        const savedPath = await ipcRenderer.invoke('save-image', dataURL, defaultName);
        if (savedPath) {
            alert(`图片已保存到: ${savedPath}`);
        }
    } catch (error) {
        console.error('保存失败:', error);
        alert('保存失败: ' + error.message);
    }
}

// 图片列表管理
function selectAllImages() {
    selectedImages.forEach(img => img.selected = true);
    updateImageList();
}

function deselectAllImages() {
    selectedImages.forEach(img => img.selected = false);
    updateImageList();
}

function clearImageList() {
    if (confirm('确定要清空图片列表吗？')) {
        selectedImages = [];
        currentImageIndex = 0;
        canvas.clear();
        updateImageCounter();
        updateImageList();
        updateNavigationButtons();
    }
}

// 上传选中的图片
async function uploadSelectedImages() {
    const selectedFiles = selectedImages.filter(img => img.selected);
    if (selectedFiles.length === 0) {
        alert('请先选择要上传的图片');
        return;
    }
    
    if (!confirm(`确定要上传 ${selectedFiles.length} 张图片吗？`)) {
        return;
    }
    
    try {
        showProgress(true);
        const client = new OSS(ossConfig);
        const results = [];
        
        for (let i = 0; i < selectedFiles.length; i++) {
            const file = selectedFiles[i];
            const progress = ((i + 1) / selectedFiles.length) * 100;
            updateProgress(progress, `正在上传 ${i + 1}/${selectedFiles.length}: ${file.name}`);
            
            // 读取文件
            const fs = require('fs');
            const buffer = fs.readFileSync(file.path);
            
            // 生成文件名
            const timestamp = Date.now();
            const ext = file.name.split('.').pop();
            const fileName = `printphoto/image_${timestamp}_${i}.${ext}`;
            
            // 上传
            const result = await client.put(fileName, buffer);
            results.push({
                name: file.name,
                url: result.url
            });
        }
        
        showProgress(false);
        
        // 显示结果
        let message = '上传完成！\n\n';
        results.forEach(result => {
            message += `${result.name}: ${result.url}\n`;
        });
        alert(message);
        
    } catch (error) {
        console.error('批量上传失败:', error);
        showProgress(false);
        alert('批量上传失败: ' + error.message);
    }
}

// 导航功能
function showPreviousImage() {
    if (currentImageIndex > 0) {
        showImage(currentImageIndex - 1);
    }
}

function showNextImage() {
    if (currentImageIndex < selectedImages.length - 1) {
        showImage(currentImageIndex + 1);
    }
}

// 更新界面
function updateImageCounter() {
    const counter = document.getElementById('imageCounter');
    const count = document.getElementById('imageCount');
    
    if (selectedImages.length === 0) {
        counter.textContent = '0 / 0';
        count.textContent = '0';
    } else {
        counter.textContent = `${currentImageIndex + 1} / ${selectedImages.length}`;
        count.textContent = selectedImages.length.toString();
    }
}

function updateNavigationButtons() {
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    
    prevBtn.disabled = currentImageIndex <= 0;
    nextBtn.disabled = currentImageIndex >= selectedImages.length - 1;
}

function updateImageList() {
    const container = document.getElementById('imageList');
    container.innerHTML = '';
    
    selectedImages.forEach((image, index) => {
        const imageItem = document.createElement('div');
        imageItem.className = 'image-item';
        if (image.selected) imageItem.classList.add('selected');
        if (index === currentImageIndex) imageItem.classList.add('current');
        
        imageItem.innerHTML = `
            <img src="file://${image.path}" class="image-thumbnail" alt="${image.name}">
            <input type="checkbox" class="image-checkbox" ${image.selected ? 'checked' : ''}>
            <div class="image-filename">${image.name}</div>
        `;
        
        // 点击缩略图显示图片
        imageItem.querySelector('.image-thumbnail').addEventListener('click', () => {
            showImage(index);
        });
        
        // 复选框事件
        const checkbox = imageItem.querySelector('.image-checkbox');
        checkbox.addEventListener('change', (e) => {
            image.selected = e.target.checked;
            updateImageList();
        });
        
        container.appendChild(imageItem);
    });
}

// 进度条控制
function showProgress(show) {
    const progressContainer = document.getElementById('uploadProgress');
    progressContainer.style.display = show ? 'block' : 'none';
    if (!show) {
        updateProgress(0, '');
    }
}

function updateProgress(percentage, text) {
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    
    progressFill.style.width = percentage + '%';
    progressText.textContent = text || `${Math.round(percentage)}%`;
} 