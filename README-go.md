# PrintPhoto - Go版本

## 📷 图片处理和打印应用

这是一个使用Go + Wails框架开发的桌面应用，提供图片处理、编辑、上传和打印功能。

### ✨ 主要功能

1. **图片选择和管理**
   - 支持多文件选择
   - 缩略图预览列表
   - 图片导航控制

2. **图片编辑**
   - 画布式图片编辑
   - 文字添加和编辑
   - 实时预览

3. **云端集成**
   - 阿里云OSS自动上传
   - 批量上传功能
   - 上传进度显示

4. **二维码生成**
   - 基于文本或图片URL生成二维码
   - 自动上传并生成二维码

5. **打印和保存**
   - 本地打印功能
   - 图片保存到本地

### 🛠 技术栈

- **后端**: Go语言
- **桌面框架**: Wails v2
- **前端**: HTML + CSS + JavaScript
- **图片编辑**: Fabric.js
- **云存储**: 阿里云OSS
- **二维码**: go-qrcode

### 📦 安装和运行

#### 前置要求
- Go 1.21+
- Node.js (用于前端依赖)

#### 安装Wails
```bash
go install github.com/wailsapp/wails/v2/cmd/wails@latest
```

#### 运行应用
```bash
# 使用构建脚本（推荐）
./build.sh

# 或者手动运行
go mod tidy
wails dev
```

#### 构建发布版本
```bash
wails build
```

### 🔧 配置

#### OSS配置
在 `app.go` 文件中修改OSS配置：
```go
const (
    OSSEndpoint        = "https://oss-cn-qingdao.aliyuncs.com"
    OSSAccessKeyId     = "your-access-key-id"
    OSSAccessKeySecret = "your-access-key-secret" 
    OSSBucketName      = "your-bucket-name"
)
```

### 📁 项目结构

```
printphoto/
├── main.go              # 主程序入口
├── app.go               # 应用逻辑和API
├── go.mod               # Go模块配置
├── frontend/            # 前端资源
│   ├── index.html       # 主界面
│   └── app.js          # 前端逻辑
├── build.sh            # 构建脚本
└── README-go.md        # 说明文档
```

### 🎯 与Electron版本的对比

| 特性 | Go + Wails | Electron |
|------|------------|----------|
| 性能 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 内存占用 | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| 打包体积 | ⭐⭐⭐⭐ | ⭐⭐ |
| 开发体验 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 跨平台 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

### 🚀 优势

1. **性能优越**: Go编译后的二进制文件执行效率高
2. **内存友好**: 相比Electron占用更少内存
3. **原生体验**: 更接近原生应用的性能和体验
4. **单文件分发**: 编译后仅需一个可执行文件
5. **安全性**: 编译型语言天然防止源码泄露

### 🔄 开发模式

```bash
# 启动开发模式（支持热重载）
wails dev

# 构建生产版本
wails build

# 构建特定平台
wails build -platform windows/amd64
wails build -platform darwin/amd64
wails build -platform linux/amd64
```

### 📝 注意事项

1. 首次运行需要安装Wails CLI工具
2. 确保Go版本在1.21+
3. macOS需要安装Xcode Command Line Tools
4. Windows需要安装Microsoft WebView2

### 🐛 故障排除

#### 常见问题

1. **Wails命令未找到**
   ```bash
   export PATH=$PATH:$(go env GOPATH)/bin
   ```

2. **前端资源加载失败**
   确保frontend目录中包含所有必要文件

3. **OSS上传失败**
   检查网络连接和OSS配置信息

### 📞 支持

如有问题请检查：
1. Go和Wails版本是否满足要求
2. 网络连接是否正常
3. OSS配置是否正确 