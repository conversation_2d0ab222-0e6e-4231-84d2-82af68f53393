<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片处理和打印应用</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        h1 {
            text-align: center;
            color: #333;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .controls {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn-warning { background: #ffc107; color: #212529; }
        .btn-danger { background: #dc3545; color: white; }
        .text-input {
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            min-width: 200px;
        }
        .main-content {
            display: flex;
            height: calc(100vh - 150px);
        }
        .canvas-container {
            flex: 2;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .sidebar {
            flex: 1;
            padding: 20px;
            border-left: 1px solid #eee;
            background: #f8f9fa;
        }
        #canvas {
            border: 2px solid #ddd;
            border-radius: 8px;
            max-width: 100%;
            max-height: 600px;
        }
        .image-nav {
            margin: 20px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .image-counter {
            font-weight: bold;
            color: #666;
        }
        .image-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
            gap: 6px;
            margin-top: 20px;
            max-height: 450px;
            overflow-y: auto;
        }
        .image-item {
            position: relative;
            border: 2px solid transparent;
            border-radius: 6px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .image-item:hover {
            border-color: #007bff;
            transform: scale(1.05);
        }
        .image-item.selected {
            border-color: #28a745;
        }
        .image-thumbnail {
            width: 100%;
            height: 45px;
            object-fit: cover;
        }
        .image-checkbox {
            position: absolute;
            top: 3px;
            right: 3px;
            width: 14px;
            height: 14px;
        }
        .image-actions {
            background: rgba(0,0,0,0.7);
            border-radius: 3px;
        }
        .image-actions .btn-tiny {
            opacity: 0.9;
        }
        .image-actions .btn-tiny:hover {
            opacity: 1;
            transform: none;
        }
        .progress-container {
            margin-top: 20px;
            display: none;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: #28a745;
            transition: width 0.3s ease;
            width: 0%;
        }
        .qr-container {
            margin-top: 20px;
            text-align: center;
        }
        .qr-code {
            max-width: 200px;
            border-radius: 8px;
        }
        .status-message {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .batch-controls {
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        .btn-small {
            padding: 5px 10px;
            font-size: 12px;
        }
        .upload-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .upload-results h4 {
            margin: 0 0 10px 0;
            color: #495057;
        }
        .upload-results-list {
            max-height: 200px;
            overflow-y: auto;
        }
        .upload-result-item {
            padding: 8px;
            margin-bottom: 8px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
            font-size: 12px;
        }
        .upload-result-item .filename {
            font-weight: bold;
            color: #495057;
            margin-bottom: 5px;
        }
        .upload-result-item .url {
            color: #007bff;
            word-break: break-all;
            cursor: pointer;
            text-decoration: underline;
            background-color: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            margin-bottom: 8px;
            display: block;
            font-family: 'Courier New', monospace;
            font-size: 11px;
        }
        .upload-result-item .url:hover {
            background-color: #e3f2fd;
        }
        .upload-result-item .actions {
            display: flex;
            gap: 5px;
            align-items: center;
        }
        .btn-tiny {
            padding: 2px 6px;
            font-size: 10px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .btn-tiny:hover {
            transform: scale(1.05);
        }
        .btn-copy {
            background: #17a2b8;
            color: white;
        }
        .btn-qr {
            background: #6f42c1;
            color: white;
        }
        .batch-badge, .processed-badge {
            position: absolute;
            top: 1px;
            left: 1px;
            font-size: 7px;
            padding: 1px 3px;
            border-radius: 2px;
            color: white;
            font-weight: bold;
            z-index: 10;
        }
        .batch-badge {
            background: #007bff;
        }
        .processed-badge {
            background: #28a745;
            left: 1px;
            top: 11px;
        }
        .switch-container {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #495057;
        }
        .switch-toggle {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .switch-input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .switch-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .switch-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        .switch-input:checked + .switch-slider {
            background-color: #007bff;
        }
        .switch-input:checked + .switch-slider:before {
            transform: translateX(26px);
        }
        .image-grid-container {
            margin-top: 20px;
            max-width: 600px;
            width: 100%;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(10, 1fr);
            gap: 4px;
            max-height: 300px;
            overflow-y: auto;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .grid-item {
            position: relative;
            aspect-ratio: 1;
            border-radius: 4px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .grid-item:hover {
            transform: scale(1.05);
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.3);
        }
        .grid-item.active {
            border-color: #28a745;
            box-shadow: 0 2px 8px rgba(40,167,69,0.4);
        }
        .grid-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .grid-item-status {
            position: absolute;
            top: 1px;
            right: 1px;
            display: flex;
            gap: 1px;
        }
        .status-icon {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            font-size: 7px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }
        .status-uploaded {
            background: #28a745;
        }
        .status-qr {
            background: #6f42c1;
        }
        .status-text {
            background: #ffc107;
            color: #212529;
        }
        
        /* 强制禁用所有图片相关文字显示 */
        img, .image-thumbnail, .grid-item img, .image-item img {
            font-size: 0 !important;
            text-indent: -9999px !important;
            overflow: hidden !important;
        }
        
        /* 禁用所有可能的伪元素内容显示 */
        img::before, img::after,
        .image-thumbnail::before, .image-thumbnail::after,
        .grid-item img::before, .grid-item img::after,
        .image-item img::before, .image-item img::after {
            content: "" !important;
            display: none !important;
        }
        
        /* 禁用任何可能的文字叠加层 */
        .image-overlay, .filename-overlay, .image-title, .image-label {
            display: none !important;
            visibility: hidden !important;
        }
        
        /* 确保没有任何文字内容在图片上显示 */
        [data-filename]::before,
        [data-filename]::after {
            content: "" !important;
            display: none !important;
        }
        
        /* 禁用浏览器默认的图片名称显示 */
        img[alt]::before,
        img[alt]::after {
            content: "" !important;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>图片处理和打印应用</h1>
        
        <div class="controls">
            <button id="selectBtn" class="btn btn-primary">选择图片</button>
            <button id="selectFolderBtn" class="btn btn-primary">选择文件夹</button>
            <input type="text" id="textInput" placeholder="输入要添加的文字" class="text-input">
            
            <!-- 新增：文字样式控制 -->
            <select id="fontSelect" class="text-input" style="min-width: 120px;">
                <option value="Arial">Arial</option>
                <option value="微软雅黑">微软雅黑</option>
                <option value="宋体">宋体</option>
                <option value="黑体">黑体</option>
                <option value="楷体">楷体</option>
                <option value="Times New Roman">Times New Roman</option>
                <option value="Helvetica">Helvetica</option>
                <option value="Georgia">Georgia</option>
            </select>
            
            <input type="color" id="textColor" value="#ff0000" title="文字颜色" style="width: 50px; height: 42px; border: 1px solid #ddd; border-radius: 5px; cursor: pointer;">
            
            <input type="range" id="fontSize" min="12" max="72" value="24" title="字体大小" style="width: 100px;">
            <span id="fontSizeLabel" style="font-size: 12px; color: #666; min-width: 30px;">24px</span>
            
            <button id="addTextBtn" class="btn btn-secondary">添加文字</button>
            <button id="clearTextBtn" class="btn btn-danger">清除文字</button>
            <button id="uploadCanvasBtn" class="btn btn-success">上传当前画布</button>
            <button id="saveAllImagesBtn" class="btn btn-info">保存所有图片</button>
            <button id="printBtn" class="btn btn-success">打印</button>
            <button id="saveBtn" class="btn btn-warning">保存</button>
        </div>
        
        <div class="main-content">
            <div class="canvas-container">
                <div class="image-nav">
                    <button id="prevBtn" class="btn btn-small btn-secondary">上一张</button>
                    <span class="image-counter" id="imageCounter">0 / 0</span>
                    <button id="nextBtn" class="btn btn-small btn-secondary">下一张</button>
                </div>
                <canvas id="canvas" width="800" height="500"></canvas>
                <div style="margin-top: 10px; font-size: 12px; color: #666; text-align: center;">
                    <div>💡 操作提示：</div>
                    <div>• 双击二维码查看内容 • 选中对象后按Delete键删除 • 拖动调整位置 • 拖动角落调整大小</div>
                    <div>• 批量处理时，每张图片会自动生成对应URL的二维码（右下角）</div>
                    <div style="color: #28a745; font-weight: bold;">🎯 保存时自动还原到原图分辨率，确保最高质量</div>
                </div>
                
                <!-- 图片网格列表 -->
                <div class="image-grid-container">
                    <h4 style="margin: 15px 0 10px 0; color: #495057; font-size: 14px;">图片列表</h4>
                    <div class="image-grid" id="imageGrid"></div>
                </div>
            </div>
            
            <div class="sidebar">
                <h3>图片列表</h3>
                <div class="batch-controls">
                    <button id="selectAllBtn" class="btn btn-small btn-info">全选</button>
                    <button id="deselectAllBtn" class="btn btn-small btn-secondary">取消全选</button>
                    <button id="uploadSelectedBtn" class="btn btn-small btn-success">上传选中</button>
                    <button id="clearListBtn" class="btn btn-small btn-danger">清空列表</button>
                </div>
                <div class="image-list" id="imageList"></div>
                
                <div class="progress-container" id="progressContainer">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div id="progressText">上传中...</div>
                </div>
                
                <div class="qr-container" id="qrContainer">
                    <h4>二维码</h4>
                    <div id="qrDescription" style="font-size: 12px; color: #666; margin-bottom: 10px; display: none;"></div>
                    <img id="qrCode" class="qr-code" style="display: none;">
                    <div style="margin-top: 10px;">
                        <button id="saveQRBtn" class="btn btn-small btn-info" style="display: none;">保存二维码</button>
                    </div>
                </div>
                
                <div class="upload-results" id="uploadResults" style="display: none;">
                    <h4>上传结果</h4>
                    <div id="uploadResultsList" class="upload-results-list"></div>
                    <button id="clearResultsBtn" class="btn btn-small btn-secondary" style="margin-top: 10px;">清空结果</button>
                </div>
                
                <div id="statusMessage" class="status-message" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html> 