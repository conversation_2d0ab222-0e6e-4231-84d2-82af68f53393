// 全局变量
let canvas;
let selectedImages = [];
let currentImageIndex = 0;
let userTextStyle = null; // 保存用户自定义的文字样式
let previewTextObject = null; // 预览文字对象
let globalTextObjects = []; // 全局文字对象数组，用于同步到所有图片

// 🎯 新增：原图尺寸和缩放比例记录
let originalImageSize = { width: 0, height: 0 }; // 当前图片的原始尺寸
let canvasScale = 1; // Canvas相对于原图的缩放比例

console.log('📍 === JavaScript文件开始加载 ===');
console.log('📍 当前时间:', new Date().toLocaleString());

// 二维码位置同步相关变量
let globalQRCodePosition = { left: 0, top: 0, scaleX: 1, scaleY: 1 }; // 全局二维码位置和大小
let lastQRCodeMovement = null; // 最后一次二维码移动记录

// 二维码移动状态变量
let isQRCodeMoving = false;
let draggedQRCode = null;
let originalQRPositions = new Map();

console.log('📍 全局变量初始化完成');

// 测试函数：手动测试二维码移动功能
window.testQRCodeMoving = function() {
    console.log('📍 === 开始测试二维码移动功能 ===');
    
    if (!canvas) {
        console.log('❌ Canvas未初始化');
        return;
    }
    
    const qrCodes = canvas.getObjects().filter(obj => obj.type === 'qrcode');
    console.log(`📍 画布上的二维码数量: ${qrCodes.length}`);
    
    qrCodes.forEach((qr, index) => {
        console.log(`📍 二维码 ${index + 1}:`, {
            type: qr.type,
            selectable: qr.selectable,
            moveable: qr.moveable,
            linkedFilename: qr.linkedFilename,
            position: `(${Math.round(qr.left)}, ${Math.round(qr.top)})`
        });
    });
    
    // 测试事件监听器
    console.log('📍 测试移动事件变量:');
    console.log('- isQRCodeMoving:', isQRCodeMoving);
    console.log('- draggedQRCode:', draggedQRCode);
    console.log('- originalQRPositions size:', originalQRPositions.size);
    console.log('- globalQRCodePosition:', globalQRCodePosition);
    
    console.log('📍 === 测试完成 ===');
    console.log('📍 请在浏览器中手动拖动二维码，观察控制台输出');
};

console.log('📍 测试函数已注册，可在控制台中执行 testQRCodeMoving() 来测试');

// 测试函数：检查当前图片的二维码状态
window.testImageQRState = function() {
    console.log('📍 === 检查当前图片的二维码状态 ===');
    
    if (currentImageIndex < 0 || currentImageIndex >= selectedImages.length) {
        console.log('❌ 当前图片索引无效');
        return;
    }
    
    const currentImage = selectedImages[currentImageIndex];
    console.log(`📍 当前图片: ${currentImage.name} (索引: ${currentImageIndex})`);
    
    // 检查画布上的二维码
    const qrCodesOnCanvas = canvas.getObjects().filter(obj => obj.type === 'qrcode');
    console.log(`📍 画布上二维码数量: ${qrCodesOnCanvas.length}`);
    qrCodesOnCanvas.forEach((qr, index) => {
        console.log(`📍 画布二维码 ${index + 1}:`, {
            linkedFilename: qr.linkedFilename,
            position: `(${Math.round(qr.left)}, ${Math.round(qr.top)})`,
            timestamp: qr.timestamp
        });
    });
    
    // 检查图片状态中保存的二维码信息
    console.log(`📍 图片状态中的二维码:`, {
        hasQRCode: currentImage.hasQRCode,
        qrCodeStateExists: !!(currentImage.qrCodeState),
        qrCodeStateLength: currentImage.qrCodeState ? currentImage.qrCodeState.length : 0
    });
    
    if (currentImage.qrCodeState && currentImage.qrCodeState.length > 0) {
        console.log(`📍 保存的二维码状态详情:`);
        currentImage.qrCodeState.forEach((qrState, index) => {
            console.log(`📍 状态 ${index + 1}:`, {
                linkedFilename: qrState.linkedFilename,
                position: `(${qrState.left}, ${qrState.top})`,
                timestamp: qrState.timestamp
            });
        });
    }
    
    // 检查全局位置
    console.log(`📍 全局二维码位置: (${globalQRCodePosition.left}, ${globalQRCodePosition.top})`);
    
    console.log('📍 === 检查完成 ===');
};

console.log('📍 图片状态测试函数已注册，可在控制台中执行 testImageQRState() 来测试');

// 测试函数：验证跨图片二维码同步功能
window.testQRCodeSync = function() {
    console.log('📍 === 测试跨图片二维码同步功能 ===');
    
    if (selectedImages.length < 2) {
        console.log('❌ 需要至少2张图片才能测试同步功能');
        return;
    }
    
    // 显示所有图片的二维码状态
    console.log(`📍 当前选中图片数量: ${selectedImages.length}`);
    console.log(`📍 当前图片索引: ${currentImageIndex}`);
    console.log(`📍 全局二维码位置: (${globalQRCodePosition.left}, ${globalQRCodePosition.top})`);
    
    selectedImages.forEach((imageInfo, index) => {
        console.log(`📍 图片 ${index}: ${imageInfo.name}`);
        console.log(`  - hasQRCode: ${imageInfo.hasQRCode}`);
        console.log(`  - isUploaded: ${imageInfo.isUploaded}`);
        console.log(`  - qrCodeData: ${!!imageInfo.qrCodeData}`);
        console.log(`  - qrCodeState存在: ${!!(imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0)}`);
        
        if (imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0) {
            imageInfo.qrCodeState.forEach((qrState, qrIndex) => {
                console.log(`  - 二维码状态 ${qrIndex + 1}: 位置 (${qrState.left}, ${qrState.top}), 文件名: ${qrState.linkedFilename}`);
            });
        }
    });
    
    // 检查位置一致性
    let positionsConsistent = true;
    const targetLeft = globalQRCodePosition.left;
    const targetTop = globalQRCodePosition.top;
    
    selectedImages.forEach((imageInfo, index) => {
        if (imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0) {
            imageInfo.qrCodeState.forEach(qrState => {
                if (Math.abs(qrState.left - targetLeft) > 1 || Math.abs(qrState.top - targetTop) > 1) {
                    console.log(`❌ 位置不一致: ${imageInfo.name} 位置 (${qrState.left}, ${qrState.top}) vs 目标 (${targetLeft}, ${targetTop})`);
                    positionsConsistent = false;
                }
            });
        }
    });
    
    if (positionsConsistent) {
        console.log('✅ 所有图片的二维码位置已同步');
    } else {
        console.log('❌ 存在位置不同步的图片');
    }
    
    console.log('📍 === 测试完成 ===');
};

console.log('📍 跨图片同步测试函数已注册，可在控制台中执行 testQRCodeSync() 来测试');

// 测试函数：验证切换图片后二维码位置同步
window.testImageSwitching = function() {
    console.log('📍 === 测试图片切换后二维码位置同步 ===');
    
    if (selectedImages.length < 2) {
        console.log('❌ 需要至少2张图片才能测试切换功能');
        return;
    }
    
    const originalIndex = currentImageIndex;
    console.log(`📍 当前图片索引: ${originalIndex}`);
    console.log(`📍 全局二维码位置: (${globalQRCodePosition.left}, ${globalQRCodePosition.top})`);
    
    // 测试切换到下一张图片
    const nextIndex = (originalIndex + 1) % selectedImages.length;
    console.log(`📍 切换到图片 ${nextIndex}: ${selectedImages[nextIndex].name}`);
    
    loadImageToCanvas(nextIndex);
    
    // 等待加载完成后检查
    setTimeout(() => {
        const qrCodesOnCanvas = canvas.getObjects().filter(obj => obj.type === 'qrcode');
        console.log(`📍 切换后画布上的二维码数量: ${qrCodesOnCanvas.length}`);
        
        if (qrCodesOnCanvas.length > 0) {
            qrCodesOnCanvas.forEach(qr => {
                const qrPosition = `(${Math.round(qr.left)}, ${Math.round(qr.top)})`;
                const globalPosition = `(${Math.round(globalQRCodePosition.left)}, ${Math.round(globalQRCodePosition.top)})`;
                const positionMatch = Math.abs(qr.left - globalQRCodePosition.left) < 2 && 
                                    Math.abs(qr.top - globalQRCodePosition.top) < 2;
                
                console.log(`📍 二维码 ${qr.linkedFilename}:`);
                console.log(`  - 画布位置: ${qrPosition}`);
                console.log(`  - 全局位置: ${globalPosition}`);
                console.log(`  - 位置匹配: ${positionMatch ? '✅' : '❌'}`);
            });
        } else {
            console.log('❌ 切换后画布上没有二维码');
        }
        
        console.log('📍 === 测试完成 ===');
    }, 1000);
};

// 测试函数：手动强制同步所有图片
window.testForceSync = function() {
    console.log('📍 === 手动强制同步测试 ===');
    forceQRCodePositionSync();
    console.log('📍 强制同步完成，请切换图片验证效果');
};

console.log('📍 图片切换测试函数已注册:');
console.log('📍 - testImageSwitching() 测试切换图片后位置同步');
console.log('📍 - testForceSync() 手动强制同步所有图片');

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Fabric.js画布
    canvas = new fabric.Canvas('canvas');
    
    // 添加画布双击事件监听器
    canvas.on('mouse:dblclick', function(e) {
        if (e.target && e.target.type === 'qrcode') {
            const content = e.target.originalContent || '未知内容';
            const timestamp = e.target.timestamp ? new Date(e.target.timestamp).toLocaleString() : '未知时间';
            alert(`二维码内容：\n${content}\n\n生成时间：${timestamp}`);
        }
    });
    
    // 添加二维码同步拖动功能
    // let isQRCodeMoving = false;  // 已移动到全局作用域
    // let draggedQRCode = null;     // 已移动到全局作用域
    // let originalQRPositions = new Map();  // 已移动到全局作用域
    
    // 监听对象移动开始事件
    canvas.on('object:moving', function(e) {
        const movingObject = e.target;
        
        // 只处理二维码对象
        if (movingObject && movingObject.type === 'qrcode') {
            // 检查是否是新的拖动操作（对象改变或状态重置）
            if (!isQRCodeMoving || draggedQRCode !== movingObject) {
                console.log(`📍 开始新的拖动操作: 对象=${movingObject.linkedFilename}, 之前状态=${isQRCodeMoving}, 之前对象=${draggedQRCode ? draggedQRCode.linkedFilename : 'null'}`);
                
                isQRCodeMoving = true;
                draggedQRCode = movingObject;
                
                // 保存所有二维码的当前位置作为原始位置
                const allQRCodes = canvas.getObjects().filter(obj => obj.type === 'qrcode');
                originalQRPositions.clear();
                allQRCodes.forEach(qrCode => {
                    originalQRPositions.set(qrCode, {
                        left: qrCode.left,
                        top: qrCode.top
                    });
                });
                
                console.log(`📍 开始拖动二维码: ${movingObject.linkedFilename || '未知'}，共有 ${allQRCodes.length} 个二维码`);
                console.log(`📍 设置isQRCodeMoving = true，原始位置映射大小: ${originalQRPositions.size}`);
                console.log(`📍 当前移动对象的原始位置:`, originalQRPositions.get(movingObject));
            }
            
            // 计算移动距离
            const originalPos = originalQRPositions.get(movingObject);
            if (originalPos) {
                const deltaX = movingObject.left - originalPos.left;
                const deltaY = movingObject.top - originalPos.top;
                
                // 只在移动距离较大时输出日志，避免刷屏
                if (Math.abs(deltaX) > 1 || Math.abs(deltaY) > 1) {
                    console.log(`📍 移动中: ${movingObject.linkedFilename}，距离: (${Math.round(deltaX)}, ${Math.round(deltaY)})`);
                }
                
                // 获取所有其他二维码对象，同步移动相同的距离
                const allOtherQRCodes = canvas.getObjects().filter(obj => 
                    obj.type === 'qrcode' && obj !== movingObject
                );
                
                allOtherQRCodes.forEach(qrCode => {
                    const otherOriginalPos = originalQRPositions.get(qrCode);
                    if (otherOriginalPos) {
                        qrCode.set({
                            left: otherOriginalPos.left + deltaX,
                            top: otherOriginalPos.top + deltaY,
                            scaleX: movingObject.scaleX || 1,
                            scaleY: movingObject.scaleY || 1
                        });
                        qrCode.setCoords();
                    }
                });
                
                // 实时更新全局位置和大小（但不保存到状态，在moved事件中才保存）
                globalQRCodePosition = {
                    left: movingObject.left,
                    top: movingObject.top,
                    scaleX: movingObject.scaleX || 1,
                    scaleY: movingObject.scaleY || 1
                };
                
                canvas.renderAll();
            } else {
                console.log(`📍 ❌ 移动中错误：找不到原始位置！movingObject=${movingObject.linkedFilename}, 映射大小=${originalQRPositions.size}`);
                console.log(`📍 所有映射的对象:`, Array.from(originalQRPositions.keys()).map(obj => obj.linkedFilename));
            }
        }
    });
    
    // 监听对象移动结束事件
    canvas.on('object:moved', function(e) {
        const movedObject = e.target;
        
        // 只处理二维码对象
        if (movedObject && movedObject.type === 'qrcode' && isQRCodeMoving) {
            console.log(`📍 二维码拖动结束: ${movedObject.linkedFilename || '未知'}，新位置: (${Math.round(movedObject.left)}, ${Math.round(movedObject.top)})`);
            console.log(`📍 当前isQRCodeMoving状态: ${isQRCodeMoving}`);
            console.log(`📍 移动对象详情:`, {
                type: movedObject.type,
                linkedFilename: movedObject.linkedFilename,
                left: movedObject.left,
                top: movedObject.top
            });
            
            // 最终确保所有二维码都移动了相同的距离
            const originalPos = originalQRPositions.get(movedObject);
            if (originalPos) {
                const deltaX = movedObject.left - originalPos.left;
                const deltaY = movedObject.top - originalPos.top;
                
                console.log(`📍 移动距离: deltaX=${Math.round(deltaX)}, deltaY=${Math.round(deltaY)}`);
                console.log(`📍 原始位置:`, originalPos);
                console.log(`📍 新位置: (${Math.round(movedObject.left)}, ${Math.round(movedObject.top)})`);
                
                const allOtherQRCodes = canvas.getObjects().filter(obj => 
                    obj.type === 'qrcode' && obj !== movedObject
                );
                
                console.log(`📍 需要同步移动的其他二维码数量: ${allOtherQRCodes.length}`);
                
                allOtherQRCodes.forEach(qrCode => {
                    const otherOriginalPos = originalQRPositions.get(qrCode);
                    if (otherOriginalPos) {
                        qrCode.set({
                            left: otherOriginalPos.left + deltaX,
                            top: otherOriginalPos.top + deltaY
                        });
                        qrCode.setCoords();
                        console.log(`📍 同步移动二维码: ${qrCode.linkedFilename}，新位置: (${Math.round(qrCode.left)}, ${Math.round(qrCode.top)})`);
                    }
                });
                
                // 更新全局二维码位置和大小
                const oldGlobalPosition = globalQRCodePosition ? {...globalQRCodePosition} : null;
                globalQRCodePosition = {
                    left: movedObject.left,
                    top: movedObject.top,
                    scaleX: movedObject.scaleX || 1,
                    scaleY: movedObject.scaleY || 1
                };
                
                // 记录最后一次移动
                lastQRCodeMovement = {
                    timestamp: Date.now(),
                    position: { ...globalQRCodePosition },
                    filename: movedObject.linkedFilename || '未知'
                };
                
                console.log(`📍 全局位置和大小更新: 从 ${oldGlobalPosition ? `(${Math.round(oldGlobalPosition.left)}, ${Math.round(oldGlobalPosition.top)}, ${oldGlobalPosition.scaleX}x${oldGlobalPosition.scaleY})` : 'null'} 到 (${Math.round(globalQRCodePosition.left)}, ${Math.round(globalQRCodePosition.top)}, ${globalQRCodePosition.scaleX}x${globalQRCodePosition.scaleY})`);
                
                // 先保存当前图片的状态（包括新的二维码位置）
                console.log(`📍 开始保存当前图片状态...`);
                saveCurrentImageTextState();
                console.log(`📍 完成保存当前图片状态`);
                
                // 然后同步位置到其他图片（排除当前图片）
                console.log(`📍 开始同步位置到其他图片状态...`);
                syncQRCodePositionToOtherImages();
                console.log(`📍 完成同步位置到其他图片状态`);
                
                // 最后强制同步所有图片的位置（确保一致性）
                console.log(`📍 开始强制同步所有图片位置...`);
                forceQRCodePositionSync();
                console.log(`📍 完成强制同步所有图片位置`);
            } else {
                console.log(`📍 ❌ 错误：找不到移动对象的原始位置！`);
            }
            
            // 重置状态
            console.log(`📍 重置移动状态: isQRCodeMoving从${isQRCodeMoving}变为false`);
            isQRCodeMoving = false;
            draggedQRCode = null;
            originalQRPositions.clear();
            console.log(`📍 已清除原始位置映射，当前大小: ${originalQRPositions.size}`);
            
            canvas.renderAll();
            
            // 统计所有二维码数量
            const totalQRCodes = canvas.getObjects().filter(obj => obj.type === 'qrcode').length;
            console.log(`📍 所有 ${totalQRCodes} 个二维码同步移动完成`);
        } else {
            console.log(`📍 跳过非二维码对象或非移动状态:`, {
                objectType: movedObject ? movedObject.type : 'null',
                isQRCodeMoving: isQRCodeMoving,
                hasTarget: !!movedObject
            });
        }
    });
    
    // 监听对象缩放事件
    canvas.on('object:scaled', function(e) {
        const scaledObject = e.target;
        
        // 只处理二维码对象
        if (scaledObject && scaledObject.type === 'qrcode') {
            console.log(`📏 二维码缩放: ${scaledObject.linkedFilename || '未知'}，新大小: ${scaledObject.scaleX.toFixed(2)}x${scaledObject.scaleY.toFixed(2)}`);
            
            // 同步所有其他二维码的大小
            const allOtherQRCodes = canvas.getObjects().filter(obj => 
                obj.type === 'qrcode' && obj !== scaledObject
            );
            
            console.log(`📏 需要同步缩放的其他二维码数量: ${allOtherQRCodes.length}`);
            
            allOtherQRCodes.forEach(qrCode => {
                qrCode.set({
                    scaleX: scaledObject.scaleX,
                    scaleY: scaledObject.scaleY
                });
                qrCode.setCoords();
                console.log(`📏 同步缩放二维码: ${qrCode.linkedFilename}，新大小: ${qrCode.scaleX.toFixed(2)}x${qrCode.scaleY.toFixed(2)}`);
            });
            
            // 更新全局二维码大小
            globalQRCodePosition.scaleX = scaledObject.scaleX;
            globalQRCodePosition.scaleY = scaledObject.scaleY;
            
            console.log(`📏 全局二维码大小更新: ${globalQRCodePosition.scaleX.toFixed(2)}x${globalQRCodePosition.scaleY.toFixed(2)}`);
            
            // 保存当前图片状态
            console.log(`📏 开始保存当前图片状态（包括新大小）...`);
            saveCurrentImageTextState();
            console.log(`📏 完成保存当前图片状态`);
            
            // 同步大小到其他图片
            console.log(`📏 开始同步大小到其他图片状态...`);
            syncQRCodeSizeToOtherImages();
            console.log(`📏 完成同步大小到其他图片状态`);
            
            canvas.renderAll();
            
            // 统计所有二维码数量
            const totalQRCodes = canvas.getObjects().filter(obj => obj.type === 'qrcode').length;
            console.log(`📏 所有 ${totalQRCodes} 个二维码同步缩放完成`);
        }
    });
    
    // 监听选择变化事件，确保二维码位置一致性
    canvas.on('selection:created', function(e) {
        const selectedObject = e.target;
        if (selectedObject && selectedObject.type === 'qrcode') {
            console.log(`📍 选中二维码: ${selectedObject.linkedFilename || '未知'}，位置: (${Math.round(selectedObject.left)}, ${Math.round(selectedObject.top)})，大小: ${selectedObject.scaleX.toFixed(2)}x${selectedObject.scaleY.toFixed(2)}`);
        }
    });
    
    // 添加键盘删除功能
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Delete' || e.key === 'Backspace') {
            const activeObject = canvas.getActiveObject();
            if (activeObject) {
                if (activeObject.type === 'qrcode' || activeObject.type === 'text') {
                    if (confirm('确定要删除选中的对象吗？')) {
                        // 如果是文字对象，需要从全局数组中移除
                        if (activeObject.type === 'text' && activeObject.globalTextId) {
                            const index = globalTextObjects.findIndex(gt => gt.id === activeObject.globalTextId);
                            if (index > -1) {
                                globalTextObjects.splice(index, 1);
                                console.log('已从全局文字数组中移除文字对象');
                            }
                        }
                        
                        // 如果删除的是预览文字，重置引用
                        if (activeObject === previewTextObject) {
                            previewTextObject = null;
                        }
                        
                        canvas.remove(activeObject);
                        canvas.renderAll();
                        showMessage('对象已删除', 'success');
                    }
                }
            }
        }
    });
    
    // 绑定事件
    bindEvents();
    
    // 更新界面
    updateImageCounter();
    updateImageList();
    
    // 测试事件监听器是否正确绑定
    console.log('📍 初始化完成，测试事件监听器绑定...');
    console.log('📍 Canvas对象:', canvas);
    console.log('📍 Canvas事件监听器数量:', canvas._objects ? canvas._objects.length : 'N/A');
    
    // 测试一个简单的鼠标点击事件
    canvas.on('mouse:down', function(e) {
        console.log('📍 Canvas鼠标点击事件触发');
        if (e.target) {
            console.log('📍 点击对象类型:', e.target.type);
            if (e.target.type === 'qrcode') {
                console.log('📍 点击了二维码对象:', e.target.linkedFilename);
            }
        }
    });
    
    console.log('📍 已添加测试点击事件监听器');
});

function bindEvents() {
    // 选择图片按钮
    document.getElementById('selectBtn').addEventListener('click', selectImages);
    
    // 选择文件夹按钮
    document.getElementById('selectFolderBtn').addEventListener('click', selectFolder);
    
    // 添加文字按钮
    document.getElementById('addTextBtn').addEventListener('click', addText);
    
    // 清除文字按钮
    document.getElementById('clearTextBtn').addEventListener('click', clearAllTexts);
    
    // 应用到所有选中图片按钮
    // 移除此功能，因为现在有自动同步
    // document.getElementById('applyToAllBtn').addEventListener('click', applyTextToAllSelected);
    
    // 字体选择器事件
    document.getElementById('fontSelect').addEventListener('change', function(e) {
        updatePreviewTextFont(e.target.value);
        updateSelectedTextFont(e.target.value);
        updateAutoTextPreview();
        // 同步到全局文字样式
        if (userTextStyle) {
            userTextStyle.fontFamily = e.target.value;
            syncTextToAllPreviews();
        }
    });
    
    // 颜色选择器事件
    document.getElementById('textColor').addEventListener('change', function(e) {
        updatePreviewTextColor(e.target.value);
        updateSelectedTextColor(e.target.value);
        updateAutoTextPreview();
        // 同步到全局文字样式
        if (userTextStyle) {
            userTextStyle.fill = e.target.value;
            syncTextToAllPreviews();
        }
    });
    
    // 字体大小滑块事件
    document.getElementById('fontSize').addEventListener('input', function(e) {
        const size = parseInt(e.target.value);
        document.getElementById('fontSizeLabel').textContent = size + 'px';
        updatePreviewTextSize(size);
        updateSelectedTextSize(size);
        updateAutoTextPreview();
        // 同步到全局文字样式
        if (userTextStyle) {
            userTextStyle.fontSize = size;
            syncTextToAllPreviews();
        }
    });
    
    // 监听文本输入框变化，实时显示预览文字
    document.getElementById('textInput').addEventListener('input', function(e) {
        const textContent = e.target.value.trim();
        
        // 移除自动预览功能 - 只有点击"添加文字"后才显示文字
        // 如果输入框为空，清理预览文字
        if (!textContent) {
            if (previewTextObject) {
                canvas.remove(previewTextObject);
                previewTextObject = null;
                canvas.renderAll();
            }
            return;
        }
        
        // 移除自动预览文字的调用
        // updatePreviewText(textContent);
        
        // 移除自动应用逻辑
        // const autoApplyEnabled = document.getElementById('autoApplyText').checked;
        // if (autoApplyEnabled && currentImageIndex >= 0) {
        //     // 实时更新当前画布的文字预览
        //     loadImageToCanvas(currentImageIndex);
        // }
    });
    
    // 上传当前画布按钮
    document.getElementById('uploadCanvasBtn').addEventListener('click', uploadCanvas);
    
    // 保存所有图片按钮
    document.getElementById('saveAllImagesBtn').addEventListener('click', saveAllImages);
    
    // 打印按钮
    document.getElementById('printBtn').addEventListener('click', printCanvas);
    
    // 保存按钮
    document.getElementById('saveBtn').addEventListener('click', saveCanvas);
    
    // 导航按钮
    document.getElementById('prevBtn').addEventListener('click', () => navigateImage(-1));
    document.getElementById('nextBtn').addEventListener('click', () => navigateImage(1));
    
    // 批量操作按钮
    document.getElementById('selectAllBtn').addEventListener('click', selectAllImages);
    document.getElementById('deselectAllBtn').addEventListener('click', deselectAllImages);
    document.getElementById('uploadSelectedBtn').addEventListener('click', uploadSelectedImages);
    document.getElementById('clearListBtn').addEventListener('click', clearImageList);
    
    // 清空上传结果按钮
    document.getElementById('clearResultsBtn').addEventListener('click', clearUploadResults);
    
    // 保存二维码按钮
    document.getElementById('saveQRBtn').addEventListener('click', saveQRCode);
}

// 选择图片文件
async function selectImages() {
    // 创建文件输入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.multiple = true;
    input.accept = 'image/*';
    
    input.onchange = function(e) {
        const files = Array.from(e.target.files);
        files.forEach(file => {
            const reader = new FileReader();
            reader.onload = function(event) {
                const imageInfo = {
                    path: file.name,
                    name: file.name,
                    selected: false,
                    data: event.target.result,
                    uploadedUrl: null, // 保存上传后的链接
                    isUploaded: false,  // 标记是否已上传
                    hasQRCode: false,   // 标记是否有二维码
                    qrCodeData: null,   // 保存二维码数据
                    processedWithText: false, // 标记是否已应用文字效果
                    isBatchImage: false // 标记是否为批量图片
                };
                selectedImages.push(imageInfo);
                updateImageList();
                updateImageCounter();
                
                // 如果是第一张图片，自动加载到画布
                if (selectedImages.length === 1) {
                    loadImageToCanvas(0);
                }
                updateImageGrid();
            };
            reader.readAsDataURL(file);
        });
    };
    
    input.click();
}

// 选择文件夹
async function selectFolder() {
    // 创建文件输入元素，设置为选择文件夹
    const input = document.createElement('input');
    input.type = 'file';
    input.webkitdirectory = true; // 选择文件夹
    input.multiple = true;
    
    input.onchange = function(e) {
        const files = Array.from(e.target.files);
        // 过滤出图片文件
        const imageFiles = files.filter(file => file.type.startsWith('image/'));
        
        if (imageFiles.length === 0) {
            showMessage('所选文件夹中没有找到图片文件', 'error');
            return;
        }
        
        showMessage(`找到 ${imageFiles.length} 张图片，正在加载...`, 'info');
        
        let loadedCount = 0;
        imageFiles.forEach(file => {
            const reader = new FileReader();
            reader.onload = function(event) {
                const imageInfo = {
                    path: file.webkitRelativePath || file.name,
                    name: file.name,
                    selected: true, // 默认选中所有图片
                    data: event.target.result,
                    uploadedUrl: null,
                    isUploaded: false,
                    hasQRCode: false,   // 标记是否有二维码
                    qrCodeData: null,   // 保存二维码数据
                    processedWithText: false, // 标记是否已应用文字效果
                    isBatchImage: false // 标记是否为批量图片
                };
                selectedImages.push(imageInfo);
                
                loadedCount++;
                if (loadedCount === imageFiles.length) {
                    updateImageList();
                    updateImageCounter();
                    
                    // 如果是第一次加载图片，自动加载第一张到画布
                    if (selectedImages.length === imageFiles.length) {
                        loadImageToCanvas(0);
                    }
                    
                    showMessage(`成功加载 ${imageFiles.length} 张图片，请添加文字后点击"应用到所有选中图片"`, 'success');
                    updateImageGrid();
                }
            };
            reader.readAsDataURL(file);
        });
    };
    
    input.click();
}

// 加载图片到画布
function loadImageToCanvas(index) {
    if (index < 0 || index >= selectedImages.length) return;
    
    // 先保存当前图片的状态
    saveCurrentImageTextState();
    
    currentImageIndex = index;
    const imageInfo = selectedImages[index];
    
    console.log(`📍 开始加载图片: ${imageInfo.name} (索引: ${index})`);
    console.log(`📍 图片信息:`, {
        hasQRCode: imageInfo.hasQRCode,
        isUploaded: imageInfo.isUploaded,
        qrCodeDataExists: !!imageInfo.qrCodeData,
        qrCodeStateExists: !!(imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0),
        qrCodeStateLength: imageInfo.qrCodeState ? imageInfo.qrCodeState.length : 0
    });
    
    if (imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0) {
        console.log(`📍 图片的二维码状态详情:`, imageInfo.qrCodeState);
    }
    
    // 更新图片计数器
    updateImageCounter();
    
    // 更新图片列表选择状态
    updateImageList();
    
    // 更新图片网格
    updateImageGrid();
    
    fabric.Image.fromURL(imageInfo.data, function(img) {
        console.log(`📍 图片加载成功，开始清理画布...`);
        canvas.clear();
        
        // 重置预览文字对象引用
        previewTextObject = null;
        
        // 🎯 编辑时缩小显示，保存时还原：记录原图尺寸和计算显示缩放
        originalImageSize.width = img.width;   // 记录原图宽度
        originalImageSize.height = img.height; // 记录原图高度

        // 调整图片大小适应画布（编辑显示）
        const scale = Math.min(canvas.width / img.width, canvas.height / img.height);
        canvasScale = scale; // 记录缩放比例，保存时用于还原

        img.scale(scale);
        img.set({
            left: canvas.width / 2,
            top: canvas.height / 2,
            originX: 'center',
            originY: 'center'
        });

        console.log(`📐 图片尺寸信息:`);
        console.log(`   原图尺寸: ${originalImageSize.width} x ${originalImageSize.height}`);
        console.log(`   显示尺寸: ${canvas.width} x ${canvas.height}`);
        console.log(`   缩放比例: ${canvasScale.toFixed(3)}`);
        console.log(`   质量保持: 保存时将还原到原图尺寸`);
        
        canvas.add(img);
        
        console.log(`📍 开始恢复图片状态...`);
        // 优先恢复图片的专属文字状态
        restoreImageTextState(imageInfo, scale);
        
        // 然后同步全局文字对象到当前画布（避免重复）
        syncGlobalTextToCanvas(scale);
        
        console.log(`📍 检查二维码恢复需求...`);
        
        // 检查是否需要恢复二维码（优先使用已保存的状态）
        if (imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0) {
            console.log(`📍 发现已保存的二维码状态，尝试恢复...`);
            // 如果有保存的二维码状态，通过restoreImageTextState已经处理了
            // 这里只需要检查是否成功恢复
            const restoredQRs = canvas.getObjects().filter(obj => obj.type === 'qrcode');
            console.log(`📍 已恢复的二维码数量: ${restoredQRs.length}`);
        } else if (imageInfo.hasQRCode && imageInfo.qrCodeData && imageInfo.uploadedUrl) {
            // 如果没有保存的状态但有二维码数据，检查画布上是否已有该图片的二维码
            const existingQR = canvas.getObjects().find(obj => 
                obj.type === 'qrcode' && obj.linkedFilename === imageInfo.name
            );
            
            if (!existingQR) {
                console.log(`📍 自动添加二维码到画布: ${imageInfo.name}`);
                console.log('二维码URL:', imageInfo.uploadedUrl);
                
                // 使用全局位置（如果存在）
                let qrPosition = { left: globalQRCodePosition.left, top: globalQRCodePosition.top };
                if (qrPosition.left === 0 && qrPosition.top === 0) {
                    // 如果全局位置为默认值，使用一个合理的默认位置
                    qrPosition = { left: canvas.width - 100, top: canvas.height - 100 };
                }
                
                addQRCodeToCanvasFromData(imageInfo.qrCodeData, imageInfo.uploadedUrl, imageInfo.name, qrPosition);
            } else {
                console.log(`📍 画布上已有二维码: ${imageInfo.name}`);
            }
        } else {
            console.log(`📍 无法添加二维码到画布: ${imageInfo.name}`);
            console.log('原因分析:', {
                hasQRCode: imageInfo.hasQRCode,
                qrCodeData: !!imageInfo.qrCodeData,
                uploadedUrl: !!imageInfo.uploadedUrl,
                hasQRCodeState: !!(imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0)
            });
        }
        
        console.log(`📍 图片加载完成: ${imageInfo.name}`);
        
        // 在加载完成后，检查是否需要强制同步二维码位置
        if (globalQRCodePosition.left !== 0 || globalQRCodePosition.top !== 0) {
            console.log(`📍 检测到全局二维码位置已设置，检查当前图片是否需要同步...`);
            
            // 如果当前图片应该有二维码但画布上没有，尝试添加
            if ((imageInfo.hasQRCode || imageInfo.qrCodeData || imageInfo.isUploaded) && 
                canvas.getObjects().filter(obj => obj.type === 'qrcode').length === 0) {
                
                console.log(`📍 当前图片应该有二维码但画布上没有，尝试添加...`);
                
                if (imageInfo.qrCodeData) {
                    // 有二维码数据，直接添加
                    const qrPosition = { left: globalQRCodePosition.left, top: globalQRCodePosition.top };
                    addQRCodeToCanvasFromData(imageInfo.qrCodeData, imageInfo.uploadedUrl || '', imageInfo.name, qrPosition);
                    console.log(`📍 已使用全局位置添加二维码: (${Math.round(qrPosition.left)}, ${Math.round(qrPosition.top)})`);
                } else {
                    console.log(`📍 图片没有二维码数据，无法添加`);
                }
            }
        }
        
        // 在加载完成后，统计画布上的二维码数量
        const qrCodesOnCanvas = canvas.getObjects().filter(obj => obj.type === 'qrcode');
        console.log(`📍 画布上当前二维码数量: ${qrCodesOnCanvas.length}`);
        qrCodesOnCanvas.forEach(qr => {
            console.log(`📍 画布二维码: ${qr.linkedFilename}，位置: (${Math.round(qr.left)}, ${Math.round(qr.top)})`);
        });
    });
}

// 清理重复文字对象的函数
function cleanupDuplicateTexts() {
    const textObjects = canvas.getObjects().filter(obj => obj.type === 'text');
    const seenTexts = new Map();
    const toRemove = [];
    
    textObjects.forEach(textObj => {
        const key = `${textObj.text}-${textObj.left}-${textObj.top}`;
        if (seenTexts.has(key)) {
            // 发现重复文字，标记为待删除
            toRemove.push(textObj);
        } else {
            seenTexts.set(key, textObj);
        }
    });
    
    // 删除重复的文字对象
    toRemove.forEach(textObj => {
        canvas.remove(textObj);
        // 如果是全局文字对象，也从数组中移除
        if (textObj.globalTextId) {
            const index = globalTextObjects.findIndex(gt => gt.id === textObj.globalTextId);
            if (index > -1) {
                globalTextObjects.splice(index, 1);
            }
        }
    });
    
    if (toRemove.length > 0) {
        canvas.renderAll();
        console.log(`清理了 ${toRemove.length} 个重复的文字对象`);
    }
}

// 同步全局文字对象到当前画布
function syncGlobalTextToCanvas(scale = 1) {
    // 先清理重复文字
    cleanupDuplicateTexts();
    
    globalTextObjects.forEach(textInfo => {
        // 检查画布上是否已有相同的文字对象
        const existingText = canvas.getObjects().find(obj => 
            obj.type === 'text' && obj.globalTextId === textInfo.id
        );
        
        if (!existingText) {
            // 创建新的文字对象
            const textObj = new fabric.Text(textInfo.text, {
                left: textInfo.left,
                top: textInfo.top,
                originX: textInfo.originX,
                originY: textInfo.originY,
                fontFamily: textInfo.fontFamily,
                fontSize: textInfo.fontSize,
                fill: textInfo.fill,
                fontWeight: 'bold',
                // 添加缩放属性同步
                scaleX: textInfo.scaleX || 1,
                scaleY: textInfo.scaleY || 1,
                angle: textInfo.angle || 0,
                selectable: true,
                // 添加阴影效果
                shadow: {
                    color: 'rgba(0, 0, 0, 0.5)',
                    blur: 3,
                    offsetX: 2,
                    offsetY: 2
                }
            });
            
            // 标记为同步的文字对象
            textObj.set('isSyncedText', true);
            textObj.set('globalTextId', textInfo.id);
            
            canvas.add(textObj);
            addTextEventListeners(textObj);
            
            console.log(`同步添加文字到画布: "${textInfo.text}" (ID: ${textInfo.id})`);
        } else {
            // 如果已存在，更新其属性以同步最新状态
            existingText.set({
                text: textInfo.text,
                left: textInfo.left,
                top: textInfo.top,
                originX: textInfo.originX,
                originY: textInfo.originY,
                fontFamily: textInfo.fontFamily,
                fontSize: textInfo.fontSize,
                fill: textInfo.fill,
                fontWeight: 'bold',
                scaleX: textInfo.scaleX || 1,
                scaleY: textInfo.scaleY || 1,
                angle: textInfo.angle || 0,
                // 添加阴影效果
                shadow: {
                    color: 'rgba(0, 0, 0, 0.5)',
                    blur: 3,
                    offsetX: 2,
                    offsetY: 2
                }
            });
            
            // 确保事件监听器已绑定
            if (!existingText._hasEventListeners) {
                addTextEventListeners(existingText);
                existingText._hasEventListeners = true;
            }
            
            console.log(`更新现有文字对象: "${textInfo.text}" (ID: ${textInfo.id})`);
        }
    });
    
    canvas.renderAll();
    console.log(`画布同步完成，当前画布文字对象数量: ${canvas.getObjects().filter(obj => obj.type === 'text').length}`);
}

// 同步文字样式到所有图片预览
function syncTextToAllPreviews() {
    if (!userTextStyle) return;
    
    // 更新预览文字对象
    if (previewTextObject) {
        previewTextObject.set({
            fontFamily: userTextStyle.fontFamily,
            fontSize: userTextStyle.fontSize,
            fill: userTextStyle.fill,
            fontWeight: userTextStyle.fontWeight || 'bold',
            // 添加阴影效果
            shadow: {
                color: 'rgba(0, 0, 0, 0.5)',
                blur: 3,
                offsetX: 2,
                offsetY: 2
            }
        });
        canvas.renderAll();
    }
    
    // 更新当前画布上的同步文字对象
    const syncedTexts = canvas.getObjects().filter(obj => obj.isSyncedText);
    syncedTexts.forEach(textObj => {
        textObj.set({
            fontFamily: userTextStyle.fontFamily,
            fontSize: userTextStyle.fontSize,
            fill: userTextStyle.fill,
            fontWeight: userTextStyle.fontWeight || 'bold',
            // 添加阴影效果
            shadow: {
                color: 'rgba(0, 0, 0, 0.5)',
                blur: 3,
                offsetX: 2,
                offsetY: 2
            }
        });
    });
    
    canvas.renderAll();
}

// 自动添加文字到画布（基于当前图片）
function addAutoText(textContent, backgroundImg, scale) {
    // 优先使用保存的用户样式，否则使用默认样式
    let textStyle = {
        fontFamily: 'Arial',
        fontSize: 24,
        fill: '#000000',
        fontWeight: 'bold',
        originX: 'center',
        originY: 'center',
        left: canvas.width / 2,
        top: canvas.height / 2
    };
    
    if (userTextStyle) {
        // 使用用户保存的样式
        textStyle = {
            fontFamily: userTextStyle.fontFamily,
            fontSize: userTextStyle.fontSize,
            fill: userTextStyle.fill,
            fontWeight: userTextStyle.fontWeight || 'bold',
            originX: userTextStyle.originX,
            originY: userTextStyle.originY,
            left: userTextStyle.left,
            top: userTextStyle.top
        };
    } else {
        // 如果没有用户样式，尝试从画布上已有的文字获取
        const existingTexts = canvas.getObjects().filter(obj => obj.type === 'text');
        if (existingTexts.length > 0) {
            const lastText = existingTexts[existingTexts.length - 1];
            textStyle = {
                fontFamily: lastText.fontFamily,
                fontSize: lastText.fontSize,
                fill: lastText.fill,
                fontWeight: lastText.fontWeight || 'bold',
                originX: lastText.originX,
                originY: lastText.originY,
                left: lastText.left,
                top: lastText.top
            };
        }
    }
    
    // 创建文字对象
    const textObj = new fabric.Text(textContent, {
        left: textStyle.left,
        top: textStyle.top,
        originX: textStyle.originX,
        originY: textStyle.originY,
        fontFamily: textStyle.fontFamily,
        fontSize: textStyle.fontSize * scale, // 根据图片缩放调整字体大小
        fill: textStyle.fill,
        fontWeight: textStyle.fontWeight,
        selectable: true,
        // 添加阴影效果
        shadow: {
            color: 'rgba(0, 0, 0, 0.5)',
            blur: 3,
            offsetX: 2,
            offsetY: 2
        }
    });
    
    canvas.add(textObj);
    canvas.renderAll();
    
    // 为自动添加的文字对象也添加事件监听，这样用户拖动预览文字后样式会被保存
    addTextEventListeners(textObj);
}

// 添加文字
function addText() {
    const text = document.getElementById('textInput').value.trim();
    if (!text) {
        showMessage('请输入要添加的文字', 'error');
        return;
    }
    
    // 检查全局文字对象中是否已存在相同文字内容
    const existingGlobalText = globalTextObjects.find(gt => gt.text === text);
    if (existingGlobalText) {
        showMessage('该文字已存在于全局文字列表中，请修改文字内容', 'warning');
        return;
    }
    
    // 检查当前画布是否已有相同内容的文字对象
    const existingTexts = canvas.getObjects().filter(obj => 
        obj.type === 'text' && obj.text === text && !obj.isPreviewText
    );
    
    if (existingTexts.length > 0) {
        showMessage('该文字已存在于当前画布，请修改文字内容或删除现有文字', 'warning');
        return;
    }
    
    let finalTextObject = null;
    
    // 如果已经有预览文字，将其转换为正式文字
    if (previewTextObject && previewTextObject.text === text) {
        // 移除预览标识
        previewTextObject.set('isPreviewText', false);
        
        // 更新边框颜色为正式文字的颜色
        previewTextObject.set('cornerColor', '#28a745');
        previewTextObject.set('borderColor', '#28a745');
        
        finalTextObject = previewTextObject;
        
        // 重置预览文字对象引用，但不删除文字对象
        previewTextObject = null;
    } else {
        // 创建新的文字对象（备用方案）
        const fontFamily = document.getElementById('fontSelect').value;
        const textColor = document.getElementById('textColor').value;
        const fontSize = parseInt(document.getElementById('fontSize').value);
        
        finalTextObject = new fabric.Text(text, {
            left: canvas.width / 2,
            top: canvas.height / 2,
            originX: 'center',
            originY: 'center',
            fontFamily: fontFamily,
            fontSize: fontSize,
            fill: textColor,
            fontWeight: 'bold',
            // 添加阴影效果
            shadow: {
                color: 'rgba(0, 0, 0, 0.5)',
                blur: 3,
                offsetX: 2,
                offsetY: 2
            }
        });
        
        canvas.add(finalTextObject);
        canvas.setActiveObject(finalTextObject);
    }
    
        canvas.renderAll();
        
    // 更新用户文字样式
    updateUserTextStyle(finalTextObject);
    
    // 生成唯一的全局文字ID
    const globalTextId = 'text_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    
    // 将文字对象添加到全局数组中（确保只添加一次）
    const newGlobalText = {
        id: globalTextId,
        text: finalTextObject.text,
        left: finalTextObject.left,
        top: finalTextObject.top,
        originX: finalTextObject.originX,
        originY: finalTextObject.originY,
        fontFamily: finalTextObject.fontFamily,
        fontSize: finalTextObject.fontSize,
        fill: finalTextObject.fill,
        scaleX: finalTextObject.scaleX || 1,
        scaleY: finalTextObject.scaleY || 1,
        angle: finalTextObject.angle || 0
    };
    
    globalTextObjects.push(newGlobalText);
    
    // 标记为全局文字对象
    finalTextObject.set('isSyncedText', true);
    finalTextObject.set('globalTextId', globalTextId);
    
    // 为文字对象添加事件监听器
    addTextEventListeners(finalTextObject);
    
    // 立即同步到所有图片
    syncAllImageTexts();
    
    showMessage('文字已确认添加并同步到所有图片', 'success');
    
    // 移除"应用到全部选中图片"相关逻辑，因为现在自动同步已经足够
    // 检查是否有选中的图片可以应用文字效果
    // const selectedCount = selectedImages.filter(img => img.selected).length;
    // const autoApplyEnabled = document.getElementById('autoApplyText').checked;
    
    // if (selectedCount > 1) {
    //     document.getElementById('applyToAllBtn').style.display = 'inline-block';
    //     if (autoApplyEnabled) {
    //         showMessage(`文字已确认并开启自动预览，可以点击"应用到所有选中图片"将此文字效果应用到 ${selectedCount} 张图片`, 'info');
    //     } else {
    //         showMessage(`文字已确认，可以点击"应用到所有选中图片"将此文字效果应用到 ${selectedCount} 张图片`, 'info');
    //     }
    // } else if (autoApplyEnabled) {
    //     showMessage('文字已确认，自动预览模式已开启', 'success');
    // }
    
    console.log('新文字已添加到全局数组，当前全局文字对象数量:', globalTextObjects.length);
    console.log('新文字ID:', globalTextId);
}

// 更新用户文字样式
function updateUserTextStyle(textObj) {
    userTextStyle = {
        fontFamily: textObj.fontFamily,
        fontSize: textObj.fontSize,
        fill: textObj.fill,
        fontWeight: textObj.fontWeight || 'bold',
        originX: textObj.originX,
        originY: textObj.originY,
        left: textObj.left,
        top: textObj.top,
        scaleX: textObj.scaleX || 1,
        scaleY: textObj.scaleY || 1,
        angle: textObj.angle || 0
    };
}

// 为文字对象添加事件监听器
function addTextEventListeners(textObj) {
    // 检查是否已经绑定过事件监听器
    if (textObj._hasEventListeners) {
        console.log(`文字对象 "${textObj.text}" 已经绑定过事件监听器，跳过重复绑定`);
        return;
    }
    
    // 监听文字对象的各种变化事件
    textObj.on('modified', function() {
        updateUserTextStyle(textObj);
        updateGlobalTextObject(textObj);
        syncAllImageTexts(); // 强制同步所有图片的文字状态
        console.log('文字样式已更新:', userTextStyle);
    });
    
    textObj.on('moved', function() {
        updateUserTextStyle(textObj);
        updateGlobalTextObject(textObj);
        syncAllImageTexts(); // 强制同步所有图片的文字状态
        console.log('文字位置已更新:', userTextStyle);
    });
    
    textObj.on('scaled', function() {
        updateUserTextStyle(textObj);
        updateGlobalTextObject(textObj);
        syncAllImageTexts(); // 强制同步所有图片的文字状态
        console.log('文字大小已更新:', userTextStyle);
    });
    
    // 监听文字内容变化
    textObj.on('changed', function() {
        updateUserTextStyle(textObj);
        updateGlobalTextObject(textObj);
        syncAllImageTexts(); // 强制同步所有图片的文字状态
        console.log('文字内容已更新:', userTextStyle);
    });
    
    // 标记已绑定事件监听器
    textObj._hasEventListeners = true;
    console.log(`为文字对象 "${textObj.text}" (ID: ${textObj.globalTextId}) 绑定事件监听器`);
}

// 新增：同步所有图片的文字状态
function syncAllImageTexts() {
    // 立即保存当前图片的文字状态
    saveCurrentImageTextState();
    
    // 为所有其他图片更新文字状态
    selectedImages.forEach((imageInfo, index) => {
        if (index !== currentImageIndex) {
            // 为每张图片更新对应的全局文字对象状态
            if (!imageInfo.textState) {
                imageInfo.textState = [];
            }
            
            // 更新或添加全局文字对象到图片的文字状态
            globalTextObjects.forEach(globalText => {
                const existingIndex = imageInfo.textState.findIndex(ts => ts.globalTextId === globalText.id);
                
                if (existingIndex > -1) {
                    // 更新现有的文字状态
                    imageInfo.textState[existingIndex] = {
                        text: globalText.text,
                        left: globalText.left,
                        top: globalText.top,
                        originX: globalText.originX,
                        originY: globalText.originY,
                        fontFamily: globalText.fontFamily,
                        fontSize: globalText.fontSize,
                        fill: globalText.fill,
                        scaleX: globalText.scaleX || 1,
                        scaleY: globalText.scaleY || 1,
                        angle: globalText.angle || 0,
                        globalTextId: globalText.id
                    };
                } else {
                    // 添加新的文字状态
                    imageInfo.textState.push({
                        text: globalText.text,
                        left: globalText.left,
                        top: globalText.top,
                        originX: globalText.originX,
                        originY: globalText.originY,
                        fontFamily: globalText.fontFamily,
                        fontSize: globalText.fontSize,
                        fill: globalText.fill,
                        scaleX: globalText.scaleX || 1,
                        scaleY: globalText.scaleY || 1,
                        angle: globalText.angle || 0,
                        globalTextId: globalText.id
                    });
                }
            });
        }
    });
    
    // 更新当前画布上的同步文字对象
    syncTextToAllPreviews();
    
    console.log('所有图片文字状态已同步');
}

// 生成二维码
async function generateQRCode() {
    try {
        let content = document.getElementById('textInput').value.trim();
        console.log('初始文本内容:', content);
        
        // 如果没有输入文字，先上传当前画布并使用图片URL
        if (!content) {
            console.log('没有文本内容，准备上传画布...');
            const canvasData = canvas.toDataURL('image/png');
            if (window.go && window.go.main && window.go.main.App && window.go.main.App.UploadImageData) {
                console.log('开始上传画布数据...');
                const result = await window.go.main.App.UploadImageData(canvasData, 'canvas.png');
                console.log('上传结果:', result);
                
                // 检查URL字段，可能是大小写问题
                const uploadUrl = result.URL || result.url || result.Url;
                console.log('提取的URL:', uploadUrl);
                
                if (uploadUrl) {
                    content = uploadUrl;
                    console.log('使用上传后的URL:', content);
                    showMessage('图片已上传，二维码内容: ' + content, 'success');
                } else {
                    console.error('上传结果没有URL字段:', result);
                    content = '上传失败，使用默认内容';
                }
            } else {
                content = '示例二维码内容';
            }
        }
        
        console.log('最终二维码内容:', content);
        
        // 确保内容不为空
        if (!content || content.trim() === '') {
            console.error('二维码内容为空');
            showMessage('二维码内容不能为空', 'error');
            return;
        }
        
        let qrDataURL;
        
        // 生成二维码
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.GenerateQRCode) {
            console.log('调用Go生成二维码方法，内容:', content);
            qrDataURL = await window.go.main.App.GenerateQRCode(content);
            console.log('二维码生成成功');
        } else {
            // 备用方案：使用在线API
            console.log('使用在线API生成二维码');
            qrDataURL = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(content);
        }
        
        // 1. 显示在侧边栏
        document.getElementById('qrCode').src = qrDataURL;
        document.getElementById('qrCode').style.display = 'block';
        
        // 显示描述信息
        const description = document.getElementById('qrDescription');
        description.textContent = `当前二维码内容: ${content}`;
        description.style.display = 'block';
        
        // 显示保存按钮
        document.getElementById('saveQRBtn').style.display = 'inline-block';
        
        // 2. 同时添加到画布上作为可拖动对象
        fabric.Image.fromURL(qrDataURL, function(qrImg) {
            // 设置二维码的初始属性
            qrImg.set({
                left: canvas.width - 120,  // 放在右上角
                top: 20,
                scaleX: 0.5,  // 缩小到合适大小
                scaleY: 0.5,
                selectable: true,
                moveable: true,
                hasControls: true,
                hasBorders: true,
                cornerSize: 10,
                transparentCorners: false,
                cornerColor: '#007bff',
                borderColor: '#007bff'
            });
            
            // 添加标识，方便后续识别
            qrImg.set('type', 'qrcode');
            qrImg.set('originalContent', content);
            qrImg.set('timestamp', Date.now());
            
            canvas.add(qrImg);
            canvas.setActiveObject(qrImg);
            canvas.renderAll();
            
            showMessage('二维码已生成并添加到画布，可以拖动调整位置和大小', 'success');
        }, {
            crossOrigin: 'anonymous'
        });
        
    } catch (error) {
        console.error('生成二维码时出错:', error);
        showMessage('生成二维码失败: ' + error.message, 'error');
    }
}

// 打印画布
async function printCanvas() {
    try {
        if (canvas.getObjects().length === 0) {
            showMessage('画布为空，请先添加内容', 'error');
            return;
        }
        
        // 确保没有选中的对象边框显示在打印的图片中
        canvas.discardActiveObject();
        canvas.renderAll();
        
        // 获取画布内容摘要
        const objects = canvas.getObjects();
        const qrCodes = objects.filter(obj => obj.type === 'qrcode');
        const texts = objects.filter(obj => obj.type === 'text');
        const images = objects.filter(obj => obj.type === 'image' && obj.type !== 'qrcode');
        
        let contentSummary = '';
        const summaryParts = [];
        if (images.length > 0) summaryParts.push(`${images.length}张图片`);
        if (texts.length > 0) summaryParts.push(`${texts.length}个文字`);
        if (qrCodes.length > 0) summaryParts.push(`${qrCodes.length}个二维码`);
        contentSummary = summaryParts.join('、');
        
        // 🎯 转换画布为高分辨率图片
        const dataURL = await createHighResolutionCanvas();
        
        // 创建打印预览对话框
        const printDialog = document.createElement('div');
        printDialog.id = 'printDialog';
        printDialog.innerHTML = `
            <style id="printDialogStyle">
                #printDialog {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.8);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 10000;
                }
                
                .print-preview-container {
                    background: white;
                    border-radius: 8px;
                    padding: 20px;
                    max-width: 90%;
                    max-height: 90%;
                    text-align: center;
                    overflow: auto;
                    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                }
                
                .print-preview-image {
                    max-width: 100%;
                    max-height: 400px;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    margin: 15px 0;
                }
                
                .print-info {
                    color: #666;
                    font-size: 14px;
                    margin: 10px 0;
                }
                
                .print-buttons {
                    margin-top: 20px;
                    display: flex;
                    gap: 10px;
                    justify-content: center;
                }
                
                .print-btn {
                    padding: 12px 24px;
                    border: none;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 16px;
                    font-weight: bold;
                    transition: all 0.3s ease;
                }
                
                .print-btn-primary {
                    background: #007bff;
                    color: white;
                }
                
                .print-btn-primary:hover {
                    background: #0056b3;
                    transform: translateY(-1px);
                }
                
                .print-btn-secondary {
                    background: #6c757d;
                    color: white;
                }
                
                .print-btn-secondary:hover {
                    background: #545b62;
                }
                
                /* 打印样式 */
                @media print {
                    body * {
                        visibility: hidden;
                    }
                    
                    #printContent, #printContent * {
                        visibility: visible;
                    }
                    
                    #printContent {
                        position: absolute !important;
                        left: 0 !important;
                        top: 0 !important;
                        width: 100% !important;
                        height: 100vh !important;
                        display: flex !important;
                        justify-content: center !important;
                        align-items: center !important;
                        flex-direction: column !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }
                    
                    #printContent img {
                        max-width: 95% !important;
                        max-height: 85vh !important;
                        object-fit: contain !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }
                    
                    #printContent .print-footer {
                        position: absolute !important;
                        bottom: 15px !important;
                        left: 50% !important;
                        transform: translateX(-50%) !important;
                        font-size: 10px !important;
                        color: #666 !important;
                        text-align: center !important;
                    }
                }
            </style>
            
            <div class="print-preview-container">
                <h3>打印预览</h3>
                <div class="print-info">内容：${contentSummary}</div>
                <img src="${dataURL}" alt="打印预览" class="print-preview-image">
                <div class="print-info">
                    <small>点击"开始打印"将弹出打印对话框，您可以选择打印机、纸张大小等设置</small>
                </div>
                
                <div class="print-buttons">
                    <button class="print-btn print-btn-primary" onclick="startPrint()">
                        🖨️ 开始打印
                    </button>
                    <button class="print-btn print-btn-secondary" onclick="closePrintDialog()">
                        取消
                    </button>
                </div>
            </div>
            
            <!-- 隐藏的打印内容 -->
            <div id="printContent" style="display: none;">
                <img src="${dataURL}" alt="打印图片">
                <div class="print-footer">
                    <div>内容: ${contentSummary} | 打印时间: ${new Date().toLocaleString('zh-CN')}</div>
                    <div>PrintPhoto - 图片处理应用</div>
                </div>
            </div>
        `;
        
        document.body.appendChild(printDialog);
        
        // 全局函数：开始打印
        window.startPrint = function() {
            try {
                window.print();
            } catch (error) {
                alert('打印失败，请检查浏览器设置或尝试使用Ctrl+P快捷键');
                console.error('打印错误:', error);
            }
        };
        
        // 全局函数：关闭对话框
        window.closePrintDialog = function() {
            const dialog = document.getElementById('printDialog');
            const style = document.getElementById('printDialogStyle');
            if (dialog) dialog.remove();
            if (style) style.remove();
            
            // 清理全局函数
            delete window.startPrint;
            delete window.closePrintDialog;
        };
        
        // ESC键关闭
        const escHandler = function(e) {
            if (e.key === 'Escape') {
                window.closePrintDialog();
                document.removeEventListener('keydown', escHandler);
            }
        };
        document.addEventListener('keydown', escHandler);
        
        // 监听打印完成
        const afterPrintHandler = function() {
            setTimeout(() => {
                window.closePrintDialog();
                window.removeEventListener('afterprint', afterPrintHandler);
                document.removeEventListener('keydown', escHandler);
            }, 1000);
        };
        window.addEventListener('afterprint', afterPrintHandler);
        
        showMessage(`打印预览已打开，包含${contentSummary}`, 'success');
        
    } catch (error) {
        console.error('打印时出错:', error);
        showMessage('打印失败: ' + error.message, 'error');
    }
}

// 🎯 创建原图尺寸的高质量Canvas用于保存（异步版本）
async function createHighResolutionCanvas() {
    if (originalImageSize.width === 0 || originalImageSize.height === 0) {
        console.warn('⚠️ 没有原图尺寸信息，使用当前Canvas');
        return canvas.toDataURL('image/png', 1.0);
    }

    // 创建原图尺寸的临时Canvas
    const tempCanvas = new fabric.Canvas(document.createElement('canvas'));
    tempCanvas.setWidth(originalImageSize.width);
    tempCanvas.setHeight(originalImageSize.height);

    // 计算还原比例（从显示尺寸还原到原图尺寸）
    const restoreScale = 1 / canvasScale;

    console.log(`📐 创建高分辨率Canvas:`);
    console.log(`   原图尺寸: ${originalImageSize.width} x ${originalImageSize.height}`);
    console.log(`   还原比例: ${restoreScale.toFixed(3)}`);

    // 复制所有对象到高分辨率Canvas
    const objects = canvas.getObjects();
    console.log(`📋 开始复制 ${objects.length} 个对象到高分辨率Canvas`);

    // 使用Promise.all确保所有对象都正确克隆
    const clonePromises = objects.map((obj, index) => {
        return new Promise((resolve) => {
            console.log(`📋 处理对象 ${index + 1}: ${obj.type}`);

            obj.clone(function(clonedObj) {
                // 还原对象的位置和尺寸到原图比例
                clonedObj.set({
                    left: obj.left * restoreScale,
                    top: obj.top * restoreScale,
                    scaleX: obj.scaleX * restoreScale,
                    scaleY: obj.scaleY * restoreScale,
                    selectable: false,  // 保存时不需要选择功能
                    evented: false      // 保存时不需要事件
                });

                // 对于文字对象，还原字体大小
                if (obj.type === 'text') {
                    clonedObj.set({
                        fontSize: obj.fontSize * restoreScale
                    });
                }

                tempCanvas.add(clonedObj);
                console.log(`✅ 对象 ${index + 1} 已添加到高分辨率Canvas`);
                resolve();
            });
        });
    });

    // 等待所有对象克隆完成
    await Promise.all(clonePromises);

    tempCanvas.renderAll();

    // 获取高分辨率图片数据
    const highResDataURL = tempCanvas.toDataURL('image/png', 1.0);

    // 清理临时Canvas
    tempCanvas.dispose();

    console.log(`✅ 高分辨率Canvas创建完成`);
    return highResDataURL;
}

// 保存画布
async function saveCanvas() {
    try {
        if (canvas.getObjects().length === 0) {
            showMessage('画布为空，请先添加内容', 'error');
            return;
        }

        // 确保没有选中的对象边框显示在保存的图片中
        canvas.discardActiveObject();
        canvas.renderAll();

        // 🎯 使用原图尺寸的高分辨率Canvas
        const dataURL = await createHighResolutionCanvas();
        const timestamp = Date.now();
        const defaultFilename = `printphoto_${timestamp}.png`;
        
        // 优先尝试使用Go应用保存（支持文件选择对话框）
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.SaveImageData) {
            try {
                const savedPath = await window.go.main.App.SaveImageData(dataURL, defaultFilename);
            showMessage('合成图片已保存到: ' + savedPath, 'success');
                
                // 显示画布内容摘要
                displaySaveSuccess();
                return;
            } catch (error) {
                console.log('Go保存失败，尝试浏览器下载方式:', error);
            }
        }
        
        // 备用方案：使用浏览器的文件保存API（如果支持）
        if (window.showSaveFilePicker) {
            try {
                const fileHandle = await window.showSaveFilePicker({
                    suggestedName: defaultFilename,
                    types: [
                        {
                            description: 'PNG图片',
                            accept: {
                                'image/png': ['.png'],
                            },
                        },
                        {
                            description: 'JPEG图片',
                            accept: {
                                'image/jpeg': ['.jpg', '.jpeg'],
                            },
                        },
                    ],
                });
                
                // 将canvas转换为blob
                const blob = await new Promise(resolve => {
                    canvas.toBlob(resolve, 'image/png', 1.0);
                });
                
                // 写入文件
                const writable = await fileHandle.createWritable();
                await writable.write(blob);
                await writable.close();
                
                showMessage('合成图片已保存到指定位置', 'success');
                displaySaveSuccess();
                return;
                
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.log('文件保存API失败:', error);
        } else {
                    showMessage('保存已取消', 'info');
                    return;
                }
            }
        }
        
        // 如果浏览器不支持文件保存API，显示自定义文件选择对话框
        showSaveLocationDialog(dataURL, defaultFilename);
        
    } catch (error) {
        console.error('保存过程出错:', error);
        showMessage('保存失败: ' + error.message, 'error');
    }
}

// 显示保存位置选择对话框
function showSaveLocationDialog(dataURL, defaultFilename) {
    const dialog = document.createElement('div');
    dialog.id = 'saveLocationDialog';
    dialog.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;
    
    dialog.innerHTML = `
        <div style="
            background: white;
            border-radius: 8px;
            padding: 30px;
            max-width: 600px;
            width: 90%;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        ">
            <h3 style="margin-top: 0; color: #333;">📁 选择保存位置和设置</h3>
            <p style="color: #666; margin: 20px 0;">
                您可以选择保存位置、修改文件名和选择图片格式
            </p>
            
            <div style="margin: 20px 0;">
                <img src="${dataURL}" style="max-width: 300px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            
            <div style="margin: 20px 0; text-align: left;">
                <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #333;">📝 文件名：</label>
                <input type="text" id="saveFilename" value="${defaultFilename}" style="
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #ddd;
                    border-radius: 6px;
                    font-size: 14px;
                    box-sizing: border-box;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                " placeholder="输入文件名...">
            </div>
            
            <div style="margin: 20px 0; text-align: left;">
                <label style="display: block; margin-bottom: 10px; font-weight: bold; color: #333;">🎨 图片格式：</label>
                <select id="saveFormat" style="
                    width: 100%;
                    padding: 12px;
                    border: 2px solid #ddd;
                    border-radius: 6px;
                    font-size: 14px;
                    box-sizing: border-box;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    background: white;
                ">
                    <option value="png">PNG (无损压缩，推荐)</option>
                    <option value="jpeg">JPEG (文件较小)</option>
                    <option value="webp">WebP (现代格式)</option>
                </select>
            </div>
            
            <div style="margin: 30px 0;">
                <h4 style="margin-bottom: 15px; color: #333;">💾 保存方式选择：</h4>
                <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap; margin-bottom: 15px;">
                    <button onclick="chooseLocationAndSave()" style="
                        background: linear-gradient(45deg, #007bff, #0056b3);
                        color: white;
                        border: none;
                        padding: 14px 24px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        box-shadow: 0 2px 8px rgba(0,123,255,0.3);
                        transition: all 0.3s ease;
                    " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                        📂 选择保存位置
                    </button>
                    
                    <button onclick="downloadToDefaultLocation()" style="
                        background: linear-gradient(45deg, #28a745, #1e7e34);
                        color: white;
                        border: none;
                        padding: 14px 24px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 16px;
                        font-weight: bold;
                        box-shadow: 0 2px 8px rgba(40,167,69,0.3);
                        transition: all 0.3s ease;
                    " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                        💾 保存到下载文件夹
                    </button>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                    <button onclick="copyImageToClipboard()" style="
                        background: linear-gradient(45deg, #17a2b8, #138496);
                        color: white;
                        border: none;
                        padding: 12px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        box-shadow: 0 2px 6px rgba(23,162,184,0.3);
                        transition: all 0.3s ease;
                    " onmouseover="this.style.transform='translateY(-1px)'" onmouseout="this.style.transform='translateY(0)'">
                        📋 复制到剪贴板
                    </button>
                    
                    <button onclick="showManualSaveInstructions()" style="
                        background: linear-gradient(45deg, #6c757d, #545b62);
                        color: white;
                        border: none;
                        padding: 12px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        box-shadow: 0 2px 6px rgba(108,117,125,0.3);
                        transition: all 0.3s ease;
                    " onmouseover="this.style.transform='translateY(-1px)'" onmouseout="this.style.transform='translateY(0)'">
                        📁 手动保存指引
                    </button>
                    
                    <button onclick="closeSaveLocationDialog()" style="
                        background: linear-gradient(45deg, #dc3545, #c82333);
                        color: white;
                        border: none;
                        padding: 12px 20px;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                        box-shadow: 0 2px 6px rgba(220,53,69,0.3);
                        transition: all 0.3s ease;
                    " onmouseover="this.style.transform='translateY(-1px)'" onmouseout="this.style.transform='translateY(0)'">
                        ✖ 取消
                    </button>
                </div>
            </div>
            
            <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; margin-top: 20px; text-align: left;">
                <h5 style="margin: 0 0 10px 0; color: #495057;">💡 功能说明：</h5>
                <ul style="margin: 0; padding-left: 20px; color: #6c757d; font-size: 13px; line-height: 1.4;">
                    <li><strong>选择保存位置</strong>：弹出文件对话框，可自由选择保存文件夹和文件名</li>
                    <li><strong>保存到下载文件夹</strong>：直接保存到浏览器默认下载位置</li>
                    <li><strong>复制到剪贴板</strong>：复制图片数据，可粘贴到其他应用</li>
                    <li><strong>手动保存指引</strong>：显示详细的手动保存步骤</li>
                </ul>
            </div>
        </div>
    `;
    
    document.body.appendChild(dialog);
    
    // 全局函数：选择位置并保存
    window.chooseLocationAndSave = async function() {
        const filename = document.getElementById('saveFilename').value || defaultFilename;
        const format = document.getElementById('saveFormat').value;
        
        // 确保文件名有正确的扩展名
        let extension = '.png';
        if (format === 'jpeg') extension = '.jpg';
        else if (format === 'webp') extension = '.webp';
        
        let finalFilename = filename;
        if (!finalFilename.toLowerCase().endsWith(extension)) {
            finalFilename = finalFilename.replace(/\.(png|jpg|jpeg|webp)$/i, '') + extension;
        }
        
        // 优先使用Go应用的文件保存对话框
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.SaveImageData) {
            try {
                let finalDataURL = dataURL;
                if (format === 'jpeg') {
                    finalDataURL = canvas.toDataURL('image/jpeg', 0.95);
                } else if (format === 'webp') {
                    finalDataURL = canvas.toDataURL('image/webp', 0.95);
                }
                
                const savedPath = await window.go.main.App.SaveImageData(finalDataURL, finalFilename);
                showMessage('✅ 合成图片已保存到: ' + savedPath, 'success');
                displaySaveSuccess();
                closeSaveLocationDialog();
                return;
            } catch (error) {
                console.log('Go保存失败，尝试浏览器API:', error);
                showMessage('Go应用保存失败，尝试浏览器保存...', 'warning');
            }
        }
        
        // 备用方案：使用浏览器的文件保存API
        if (window.showSaveFilePicker) {
            try {
                const fileHandle = await window.showSaveFilePicker({
                    suggestedName: finalFilename,
                    types: [
                        {
                            description: 'PNG图片',
                            accept: { 'image/png': ['.png'] }
                        },
                        {
                            description: 'JPEG图片',
                            accept: { 'image/jpeg': ['.jpg', '.jpeg'] }
                        },
                        {
                            description: 'WebP图片',
                            accept: { 'image/webp': ['.webp'] }
                        }
                    ]
                });
                
                // 根据用户选择的格式生成图片
                const blob = await new Promise(resolve => {
                    const mimeType = format === 'jpeg' ? 'image/jpeg' : format === 'webp' ? 'image/webp' : 'image/png';
                    const quality = format === 'jpeg' || format === 'webp' ? 0.95 : 1.0;
                    canvas.toBlob(resolve, mimeType, quality);
                });
                
                const writable = await fileHandle.createWritable();
                await writable.write(blob);
                await writable.close();
                
                showMessage('✅ 合成图片已保存到您选择的位置', 'success');
                displaySaveSuccess();
                closeSaveLocationDialog();
                
            } catch (error) {
                if (error.name === 'AbortError') {
                    showMessage('📝 保存已取消', 'info');
                } else {
                    console.error('文件保存API失败:', error);
                    showMessage('❌ 浏览器保存失败，请尝试其他方式', 'error');
                }
            }
        } else {
            showMessage('❌ 您的浏览器不支持文件位置选择，请使用其他保存方式', 'warning');
        }
    };
    
    // 全局函数：下载到默认位置
    window.downloadToDefaultLocation = function() {
        const filename = document.getElementById('saveFilename').value || defaultFilename;
        const format = document.getElementById('saveFormat').value;
        
        let finalDataURL = dataURL;
        let extension = '.png';
        
        // 根据选择的格式转换图片
        if (format === 'jpeg') {
            finalDataURL = canvas.toDataURL('image/jpeg', 0.95);
            extension = '.jpg';
        } else if (format === 'webp') {
            finalDataURL = canvas.toDataURL('image/webp', 0.95);
            extension = '.webp';
        }
        
        // 确保文件名有正确的扩展名
        let finalFilename = filename;
        if (!finalFilename.toLowerCase().endsWith(extension)) {
            finalFilename = finalFilename.replace(/\.(png|jpg|jpeg|webp)$/i, '') + extension;
        }
        
        try {
            const link = document.createElement('a');
            link.download = finalFilename;
            link.href = finalDataURL;
            
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            
            showMessage(`✅ 图片已下载到默认位置: ${finalFilename}`, 'success');
            displaySaveSuccess();
            closeSaveLocationDialog();
            
        } catch (error) {
            console.error('下载失败:', error);
            showMessage('❌ 下载失败，请尝试其他保存方式', 'error');
        }
    };
    
    // 全局函数：复制到剪贴板
    window.copyImageToClipboard = async function() {
        if (navigator.clipboard && navigator.clipboard.write) {
            try {
                canvas.toBlob(async (blob) => {
                    const clipboardItem = new ClipboardItem({ 'image/png': blob });
                    await navigator.clipboard.write([clipboardItem]);
                    showMessage('✅ 图片已复制到剪贴板，可以粘贴到其他应用中', 'success');
                    closeSaveLocationDialog();
                }, 'image/png', 1.0);
            } catch (error) {
                console.error('复制到剪贴板失败:', error);
                showMessage('❌ 复制失败，请尝试其他保存方式', 'error');
            }
        } else {
            showMessage('❌ 您的浏览器不支持剪贴板功能', 'error');
        }
    };
    
    // 全局函数：显示手动保存指引
    window.showManualSaveInstructions = function() {
        closeSaveLocationDialog();
        const filename = document.getElementById('saveFilename').value || defaultFilename;
        showManualSaveDialog(dataURL, filename);
    };
    
    // 全局函数：关闭对话框
    window.closeSaveLocationDialog = function() {
        const dialog = document.getElementById('saveLocationDialog');
        if (dialog) {
            dialog.remove();
        }
        
        // 清理全局函数
        delete window.chooseLocationAndSave;
        delete window.downloadToDefaultLocation;
        delete window.copyImageToClipboard;
        delete window.showManualSaveInstructions;
        delete window.closeSaveLocationDialog;
    };
    
    // ESC键关闭
    const escHandler = function(e) {
        if (e.key === 'Escape') {
            window.closeSaveLocationDialog();
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
    
    // 文件名输入框获得焦点并选中文件名部分（不包括扩展名）
    setTimeout(() => {
        const filenameInput = document.getElementById('saveFilename');
        filenameInput.focus();
        const lastDotIndex = filenameInput.value.lastIndexOf('.');
        if (lastDotIndex > 0) {
            filenameInput.setSelectionRange(0, lastDotIndex);
        }
    }, 100);
    
    // 监听格式变化，自动更新文件扩展名
    document.getElementById('saveFormat').addEventListener('change', function(e) {
        const format = e.target.value;
        const filenameInput = document.getElementById('saveFilename');
        let filename = filenameInput.value;
        
        // 移除现有扩展名
        filename = filename.replace(/\.(png|jpg|jpeg|webp)$/i, '');
        
        // 添加新扩展名
        if (format === 'jpeg') {
            filename += '.jpg';
        } else if (format === 'webp') {
            filename += '.webp';
        } else {
            filename += '.png';
        }
        
        filenameInput.value = filename;
    });
}

// 显示保存成功后的内容摘要
function displaySaveSuccess() {
        const objects = canvas.getObjects();
        const qrCodes = objects.filter(obj => obj.type === 'qrcode');
        const texts = objects.filter(obj => obj.type === 'text');
    const images = objects.filter(obj => obj.type === 'image' && obj.type !== 'qrcode');
        
        let summary = `保存的图片包含: `;
    const summaryParts = [];
    if (images.length > 0) summaryParts.push(`${images.length}张图片`);
    if (texts.length > 0) summaryParts.push(`${texts.length}个文字`);
    if (qrCodes.length > 0) summaryParts.push(`${qrCodes.length}个二维码`);
    
    if (summaryParts.length === 0) {
            summary += `原始图片`;
    } else {
        summary += summaryParts.join('、');
        }
        
        console.log(summary);
}

// 显示手动保存对话框
function showManualSaveDialog(dataURL, filename) {
    const dialog = document.createElement('div');
    dialog.id = 'manualSaveDialog';
    dialog.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
    `;
    
    dialog.innerHTML = `
        <div style="
            background: white;
            border-radius: 8px;
            padding: 30px;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        ">
            <h3 style="margin-top: 0; color: #333;">手动保存图片</h3>
            <p style="color: #666; margin: 20px 0;">
                自动下载功能可能不可用，请使用以下方式保存图片：
            </p>
            
            <div style="margin: 20px 0;">
                <img src="${dataURL}" style="max-width: 300px; max-height: 200px; border: 1px solid #ddd; border-radius: 4px;">
            </div>
            
            <div style="margin: 20px 0; text-align: left;">
                <p><strong>保存方式：</strong></p>
                <ol style="color: #666;">
                    <li>右键点击上方图片</li>
                    <li>选择"图片另存为"或"保存图片"</li>
                    <li>选择保存位置并确认</li>
                </ol>
            </div>
            
            <div style="margin-top: 30px;">
                <button onclick="closeManualSaveDialog()" style="
                    background: #007bff;
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 4px;
                    cursor: pointer;
                    font-size: 16px;
                ">知道了</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(dialog);
    
    // 全局关闭函数
    window.closeManualSaveDialog = function() {
        const dialog = document.getElementById('manualSaveDialog');
        if (dialog) {
            dialog.remove();
        }
        delete window.closeManualSaveDialog;
    };
    
    // ESC键关闭
    const escHandler = function(e) {
        if (e.key === 'Escape') {
            window.closeManualSaveDialog();
            document.removeEventListener('keydown', escHandler);
        }
    };
    document.addEventListener('keydown', escHandler);
    
    showMessage('请使用右键保存图片', 'info');
}

// 图片导航
function navigateImage(direction) {
    if (selectedImages.length === 0) return;
    
    // 清理当前画布的重复文字
    cleanupDuplicateTexts();
    
    // 保存当前图片的文字状态
    saveCurrentImageTextState();
    
    let newIndex = currentImageIndex + direction;
    if (newIndex >= selectedImages.length) newIndex = 0;
    if (newIndex < 0) newIndex = selectedImages.length - 1;
    
        loadImageToCanvas(newIndex);
}

// 保存当前图片的文字状态
function saveCurrentImageTextState() {
    if (currentImageIndex >= 0 && currentImageIndex < selectedImages.length) {
        const currentImage = selectedImages[currentImageIndex];
        const textObjects = canvas.getObjects().filter(obj => obj.type === 'text' && !obj.isPreviewText);
        const qrCodeObjects = canvas.getObjects().filter(obj => obj.type === 'qrcode');
        
        console.log(`📍 正在保存图片 ${currentImage.name} 的状态...`);
        console.log(`📍 画布上的二维码数量: ${qrCodeObjects.length}`);
        
        // 保存当前图片的文字状态
        currentImage.textState = textObjects.map(textObj => ({
            text: textObj.text,
            left: textObj.left,
            top: textObj.top,
            originX: textObj.originX,
            originY: textObj.originY,
            fontFamily: textObj.fontFamily,
            fontSize: textObj.fontSize,
            fill: textObj.fill,
            scaleX: textObj.scaleX || 1,
            scaleY: textObj.scaleY || 1,
            angle: textObj.angle || 0,
            globalTextId: textObj.globalTextId
        }));
        
        // 保存当前图片的二维码状态
        currentImage.qrCodeState = qrCodeObjects.map(qrObj => {
            console.log(`📍 正在保存二维码对象:`, {
                linkedFilename: qrObj.linkedFilename,
                currentLeft: qrObj.left,
                currentTop: qrObj.top,
                originalContent: qrObj.originalContent
            });
            
            const qrState = {
                left: qrObj.left,
                top: qrObj.top,
                scaleX: qrObj.scaleX || 1,
                scaleY: qrObj.scaleY || 1,
                angle: qrObj.angle || 0,
                originalContent: qrObj.originalContent,
                linkedFilename: qrObj.linkedFilename,
                timestamp: qrObj.timestamp,
                autoGenerated: qrObj.autoGenerated
            };
            
            console.log(`📍 保存二维码状态: ${qrObj.linkedFilename}，位置: (${Math.round(qrObj.left)}, ${Math.round(qrObj.top)})`);
            console.log(`📍 保存的状态对象:`, qrState);
            
            return qrState;
        });
        
        console.log(`📍 已保存图片 ${currentImage.name} 的状态 - 文字: ${textObjects.length}个, 二维码: ${qrCodeObjects.length}个`);
        
        // 显示当前图片的qrCodeState详情
        if (currentImage.qrCodeState && currentImage.qrCodeState.length > 0) {
            console.log(`📍 当前图片的二维码状态详情:`, currentImage.qrCodeState);
        }
    } else {
        console.log(`📍 无法保存状态: currentImageIndex=${currentImageIndex}, selectedImages.length=${selectedImages.length}`);
    }
}

// 恢复图片的文字状态
function restoreImageTextState(imageInfo, scale = 1) {
    // 恢复文字状态
    if (imageInfo.textState && imageInfo.textState.length > 0) {
        imageInfo.textState.forEach(textState => {
            // 检查是否已存在相同的文字对象
            const existingText = canvas.getObjects().find(obj => 
                obj.type === 'text' && 
                obj.text === textState.text && 
                Math.abs(obj.left - textState.left) < 5 &&
                Math.abs(obj.top - textState.top) < 5
            );
            
            if (!existingText) {
                const textObj = new fabric.Text(textState.text, {
                    left: textState.left,
                    top: textState.top,
                    originX: textState.originX,
                    originY: textState.originY,
                    fontFamily: textState.fontFamily,
                    fontSize: textState.fontSize,
                    fill: textState.fill,
                    fontWeight: 'bold',
                    scaleX: textState.scaleX || 1,
                    scaleY: textState.scaleY || 1,
                    angle: textState.angle || 0,
                    selectable: true,
                    // 添加阴影效果
                    shadow: {
                        color: 'rgba(0, 0, 0, 0.5)',
                        blur: 3,
                        offsetX: 2,
                        offsetY: 2
                    }
                });
                
                if (textState.globalTextId) {
                    textObj.set('isSyncedText', true);
                    textObj.set('globalTextId', textState.globalTextId);
                }
                
                canvas.add(textObj);
                addTextEventListeners(textObj);
            }
        });
    }
    
    // 恢复二维码状态
    if (imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0) {
        console.log(`📍 开始恢复二维码状态，数量: ${imageInfo.qrCodeState.length}`);
        
        imageInfo.qrCodeState.forEach((qrState, index) => {
            console.log(`📍 正在处理二维码 ${index + 1}/${imageInfo.qrCodeState.length}: ${qrState.linkedFilename}`);
            console.log(`📍 二维码状态:`, qrState);
            
            // 检查是否已存在相同的二维码对象
            const existingQR = canvas.getObjects().find(obj => 
                obj.type === 'qrcode' && 
                obj.linkedFilename === qrState.linkedFilename
            );
            
            if (existingQR) {
                console.log(`📍 二维码已存在，跳过: ${qrState.linkedFilename}`);
                return;
            }
            
            if (!imageInfo.qrCodeData) {
                console.log(`📍 没有二维码数据，跳过: ${qrState.linkedFilename}`);
                return;
            }
            
            console.log(`📍 开始重新添加二维码到画布: ${qrState.linkedFilename}`);
            
            // 重新添加二维码到画布，使用同步的位置
            fabric.Image.fromURL(imageInfo.qrCodeData, function(qrImg) {
                // 计算二维码大小
                const qrSize = Math.max(60, Math.min(120, Math.min(canvas.width, canvas.height) * 0.08));
                
                // 优先使用全局同步位置，其次使用保存的位置，最后使用默认位置
                let qrLeft, qrTop, qrScaleX, qrScaleY;
                
                console.log(`📍 位置和大小选择分析:`);
                console.log(`  - 全局位置: (${globalQRCodePosition.left}, ${globalQRCodePosition.top})，大小: ${globalQRCodePosition.scaleX}x${globalQRCodePosition.scaleY}`);
                console.log(`  - 保存位置: (${qrState.left}, ${qrState.top})，大小: ${qrState.scaleX}x${qrState.scaleY}`);
                
                // 位置选择
                if (globalQRCodePosition.left !== 0 || globalQRCodePosition.top !== 0) {
                    // 优先使用全局位置（同步后的位置）
                    qrLeft = globalQRCodePosition.left;
                    qrTop = globalQRCodePosition.top;
                    console.log(`📍 ✅ 使用全局同步位置: (${qrLeft}, ${qrTop})`);
                } else if (qrState.left !== undefined && qrState.top !== undefined && 
                           (qrState.left !== 0 || qrState.top !== 0)) {
                    // 其次使用保存的位置
                    qrLeft = qrState.left;
                    qrTop = qrState.top;
                    console.log(`📍 ⚠️ 使用保存的位置: (${qrLeft}, ${qrTop})`);
                } else {
                    // 最后使用默认位置
                    qrLeft = canvas.width - qrSize - 20;
                    qrTop = canvas.height - qrSize - 20;
                    console.log(`📍 💡 使用默认位置: (${qrLeft}, ${qrTop})`);
                }
                
                // 大小选择
                if (globalQRCodePosition.scaleX && globalQRCodePosition.scaleY && 
                    (globalQRCodePosition.scaleX !== 1 || globalQRCodePosition.scaleY !== 1)) {
                    // 优先使用全局大小（同步后的大小）
                    qrScaleX = globalQRCodePosition.scaleX;
                    qrScaleY = globalQRCodePosition.scaleY;
                    console.log(`📏 ✅ 使用全局同步大小: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
                } else if (qrState.scaleX !== undefined && qrState.scaleY !== undefined &&
                           (qrState.scaleX !== 0 && qrState.scaleY !== 0)) {
                    // 其次使用保存的大小
                    qrScaleX = qrState.scaleX;
                    qrScaleY = qrState.scaleY;
                    console.log(`📏 ⚠️ 使用保存的大小: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
                } else {
                    // 最后使用默认大小
                    qrScaleX = qrSize / qrImg.width;
                    qrScaleY = qrSize / qrImg.height;
                    console.log(`📏 💡 使用默认大小: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
                }
                
                console.log(`📍 最终二维码位置: (${Math.round(qrLeft)}, ${Math.round(qrTop)})，大小: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
                
                // 设置二维码的属性
                qrImg.set({
                    left: qrLeft,
                    top: qrTop,
                    scaleX: qrScaleX,
                    scaleY: qrScaleY,
                    angle: qrState.angle || 0,
                    selectable: true,
                    moveable: true,
                    hasControls: true,
                    hasBorders: true,
                    cornerSize: 8,
                    transparentCorners: false,
                    cornerColor: '#28a745',
                    borderColor: '#28a745'
                });
                
                // 添加标识
                qrImg.set('type', 'qrcode');
                qrImg.set('originalContent', qrState.originalContent);
                qrImg.set('linkedFilename', qrState.linkedFilename);
                qrImg.set('timestamp', qrState.timestamp);
                qrImg.set('autoGenerated', qrState.autoGenerated);
                
                canvas.add(qrImg);
                canvas.renderAll();
                
                console.log(`📍 已恢复二维码: ${qrState.linkedFilename}，最终位置: (${Math.round(qrLeft)}, ${Math.round(qrTop)})`);
            }, {
                crossOrigin: 'anonymous'
            });
        });
    } else {
        console.log(`📍 没有二维码状态需要恢复`);
    }
}

// 更新图片计数器
function updateImageCounter() {
    const counter = document.getElementById('imageCounter');
    counter.textContent = (currentImageIndex + 1) + ' / ' + selectedImages.length;
    
    // 更新导航按钮状态
    document.getElementById('prevBtn').disabled = currentImageIndex <= 0;
    document.getElementById('nextBtn').disabled = currentImageIndex >= selectedImages.length - 1;
}

// 更新图片列表
function updateImageList() {
    const imageList = document.getElementById('imageList');
    imageList.innerHTML = '';
    
    selectedImages.forEach((imageInfo, index) => {
        const item = document.createElement('div');
        item.className = 'image-item ' + (index === currentImageIndex ? 'selected' : '');
        
        // 基础HTML结构
        let itemHTML = `
            <img src="${imageInfo.data}" class="image-thumbnail" title="">
            <input type="checkbox" class="image-checkbox" ${imageInfo.selected ? 'checked' : ''}>
        `;
        
        // 如果已上传，添加二维码按钮
        if (imageInfo.isUploaded) {
            itemHTML += `
                <div class="image-actions" style="position: absolute; bottom: 1px; left: 1px; right: 1px;">
                    <button class="btn-tiny btn-qr qr-to-canvas-btn" style="width: 100%; font-size: 8px; padding: 1px; line-height: 1.2;">
                        添加二维码
                    </button>
                </div>
            `;
        }
        
        // 添加批量处理和已处理标识
        if (imageInfo.isBatchImage) {
            itemHTML += '<div class="batch-badge">批量</div>';
        }
        if (imageInfo.processedWithText) {
            itemHTML += '<div class="processed-badge">已处理</div>';
        }
        
        item.innerHTML = itemHTML;
        
        // 点击图片项切换到该图片
        item.onclick = function(e) {
            if (e.target.type !== 'checkbox' && !e.target.classList.contains('qr-to-canvas-btn')) {
                loadImageToCanvas(index);
            }
        };
        
        // 复选框事件
        const checkbox = item.querySelector('.image-checkbox');
        checkbox.onchange = function(e) {
            e.stopPropagation();
            imageInfo.selected = checkbox.checked;
        };
        
        // 二维码按钮事件
        const qrBtn = item.querySelector('.qr-to-canvas-btn');
        if (qrBtn) {
            qrBtn.onclick = function(e) {
                e.stopPropagation();
                addQRCodeToCanvas(imageInfo.uploadedUrl, imageInfo.name);
            };
        }
        
        imageList.appendChild(item);
    });
}

// 全选图片
function selectAllImages() {
    selectedImages.forEach(img => img.selected = true);
    updateImageList();
}

// 取消全选
function deselectAllImages() {
    selectedImages.forEach(img => img.selected = false);
    updateImageList();
}

// 上传选中的图片
async function uploadSelectedImages() {
    const selectedImageData = selectedImages.filter(img => img.selected);
    if (selectedImageData.length === 0) {
        showMessage('请先选择要上传的图片', 'error');
        return;
    }
    
    try {
        showProgress(true);
        updateProgress(0, '准备上传...');
        
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.UploadImageData) {
            const results = [];
            let successCount = 0;
            
            for (let i = 0; i < selectedImageData.length; i++) {
                const imageInfo = selectedImageData[i];
                updateProgress(((i) / selectedImageData.length) * 100, `上传中... ${i + 1}/${selectedImageData.length}`);
                
                try {
                    // 上传原图获取链接，传递原始文件名
                    const result = await window.go.main.App.UploadImageData(imageInfo.data, imageInfo.name);
                    console.log(`上传结果 (${imageInfo.name}):`, result);
                    
                    // 检查URL字段，可能是大小写问题
                    const uploadUrl = result.URL || result.url || result.Url;
                    console.log('上传URL:', uploadUrl);
                    
                    if (uploadUrl) {
                        // 更新图片信息
                        imageInfo.uploadedUrl = uploadUrl;
                        imageInfo.isUploaded = true;
                        
                        // 为每张图片生成二维码数据
                        console.log(`开始为 ${imageInfo.name} 生成二维码...`);
                        const qrDataURL = await generateQRCodeForImage(uploadUrl);
                        if (qrDataURL) {
                            imageInfo.qrCodeData = qrDataURL;
                            imageInfo.hasQRCode = true;
                            console.log(`✓ 为 ${imageInfo.name} 生成了二维码，数据长度:`, qrDataURL.length);
                            console.log('二维码数据预览:', qrDataURL.substring(0, 100) + '...');
                        } else {
                            console.error(`✗ 为 ${imageInfo.name} 生成二维码失败`);
                            imageInfo.hasQRCode = false;
                            imageInfo.qrCodeData = null;
                        }
                        
                        results.push({
                            filename: imageInfo.name,
                            url: uploadUrl,
                            success: true
                        });
                        successCount++;
                        addUploadResult(imageInfo.name, uploadUrl, true);
                        console.log(`上传成功: ${imageInfo.name} -> ${uploadUrl}`);
                        
                        // 为当前选中的图片自动添加二维码到画布
                        if (i === currentImageIndex && qrDataURL) {
                            console.log(`当前图片是 ${imageInfo.name}，自动添加二维码到画布`);
                            await addQRCodeToCurrentCanvas(uploadUrl, imageInfo.name);
                        }
                        
                    } else {
                        console.error('上传结果中没有找到URL字段:', result);
                        results.push({
                            filename: imageInfo.name,
                            error: '上传成功但未返回URL',
                            success: false
                        });
                        addUploadResult(imageInfo.name, '上传成功但未返回URL', false);
                    }
                } catch (error) {
                    console.error(`上传图片 ${imageInfo.name} 失败:`, error);
                    results.push({
                        filename: imageInfo.name,
                        error: error.message,
                        success: false
                    });
                    addUploadResult(imageInfo.name, error.message, false);
                }
            }
            
            updateProgress(100, '上传完成');
            
            if (successCount > 0) {
                showMessage(`成功上传 ${successCount} 张图片，每张都已生成专属二维码`, 'success');
                showUploadResults(true);
                updateImageList(); // 更新图片列表显示
                
                // 重新加载当前图片以显示二维码
                if (currentImageIndex >= 0 && selectedImages[currentImageIndex] && selectedImages[currentImageIndex].hasQRCode) {
                    console.log(`重新加载当前图片 ${selectedImages[currentImageIndex].name} 以显示二维码`);
                    loadImageToCanvas(currentImageIndex);
                }
                updateImageGrid(); // 更新网格显示
            }
            
            if (results.length - successCount > 0) {
                showMessage(`${results.length - successCount} 张图片上传失败`, 'error');
            }
        } else {
            // 模拟上传进度
            for (let i = 0; i < selectedImageData.length; i++) {
                const progress = ((i + 1) / selectedImageData.length) * 100;
                updateProgress(progress, '上传中... ' + (i + 1) + '/' + selectedImageData.length);
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            showMessage('模拟上传 ' + selectedImageData.length + ' 张图片完成', 'success');
        }
        
        setTimeout(() => showProgress(false), 2000);
    } catch (error) {
        showMessage('上传失败: ' + error.message, 'error');
        showProgress(false);
    }
}

// 添加上传结果到列表
function addUploadResult(filename, urlOrError, success) {
    const resultsList = document.getElementById('uploadResultsList');
    const resultItem = document.createElement('div');
    resultItem.className = 'upload-result-item';
    
    const filenameDiv = document.createElement('div');
    filenameDiv.className = 'filename';
    filenameDiv.textContent = filename;
    
    const urlDiv = document.createElement('div');
    urlDiv.className = 'url';
    
    if (success) {
        urlDiv.textContent = urlOrError;
        urlDiv.style.color = '#007bff';
        urlDiv.title = '完整链接地址';
        
        // 创建操作按钮区域
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'actions';
        
        // 复制链接按钮
        const copyBtn = document.createElement('button');
        copyBtn.className = 'btn-tiny btn-copy';
        copyBtn.textContent = '复制链接';
        copyBtn.onclick = () => {
            navigator.clipboard.writeText(urlOrError).then(() => {
                showMessage('链接已复制到剪贴板', 'success');
            });
        };
        
        // 生成二维码按钮
        const qrBtn = document.createElement('button');
        qrBtn.className = 'btn-tiny btn-qr';
        qrBtn.textContent = '生成二维码';
        qrBtn.onclick = async () => {
            try {
                console.log(`为 ${filename} 生成二维码，URL:`, urlOrError);
                
                // 确保URL不为空
                if (!urlOrError || urlOrError.trim() === '') {
                    console.error('URL为空，无法生成二维码');
                    showMessage('链接为空，无法生成二维码', 'error');
                    return;
                }
                
                if (window.go && window.go.main && window.go.main.App && window.go.main.App.GenerateQRCode) {
                    console.log('调用Go方法生成二维码');
                    const qrDataURL = await window.go.main.App.GenerateQRCode(urlOrError);
                    console.log('二维码生成成功');
                    document.getElementById('qrCode').src = qrDataURL;
                    document.getElementById('qrCode').style.display = 'block';
                    
                    // 显示描述信息
                    const description = document.getElementById('qrDescription');
                    description.textContent = `${filename} 的下载链接`;
                    description.style.display = 'block';
                    
                    // 显示保存按钮
                    document.getElementById('saveQRBtn').style.display = 'inline-block';
                    
                    showMessage(`已为 ${filename} 的链接生成二维码`, 'success');
                    
                    // 滚动到二维码区域
                    document.getElementById('qrContainer').scrollIntoView({ 
                        behavior: 'smooth', 
                        block: 'nearest' 
                    });
                } else {
                    // 备用方案：使用在线API
                    console.log('使用在线API生成二维码');
                    const qrUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(urlOrError);
                    document.getElementById('qrCode').src = qrUrl;
                    document.getElementById('qrCode').style.display = 'block';
                    
                    // 显示描述信息
                    const description = document.getElementById('qrDescription');
                    description.textContent = `${filename} 的下载链接`;
                    description.style.display = 'block';
                    
                    // 显示保存按钮
                    document.getElementById('saveQRBtn').style.display = 'inline-block';
                    
                    showMessage(`已为 ${filename} 的链接生成二维码`, 'success');
                }
            } catch (error) {
                console.error('生成二维码时出错:', error);
                showMessage('生成二维码失败: ' + error.message, 'error');
            }
        };
        
        actionsDiv.appendChild(copyBtn);
        actionsDiv.appendChild(qrBtn);
        
        resultItem.appendChild(filenameDiv);
        resultItem.appendChild(urlDiv);
        resultItem.appendChild(actionsDiv);
    } else {
        urlDiv.textContent = '错误: ' + urlOrError;
        urlDiv.style.color = '#dc3545';
        
        resultItem.appendChild(filenameDiv);
        resultItem.appendChild(urlDiv);
    }
    
    resultsList.appendChild(resultItem);
    
    // 滚动到最新项目
    resultsList.scrollTop = resultsList.scrollHeight;
}

// 显示/隐藏上传结果区域
function showUploadResults(show) {
    const uploadResults = document.getElementById('uploadResults');
    uploadResults.style.display = show ? 'block' : 'none';
}

// 清空上传结果
function clearUploadResults() {
    if (confirm('确定要清空所有上传结果吗？')) {
        document.getElementById('uploadResultsList').innerHTML = '';
        showUploadResults(false);
        showMessage('上传结果已清空', 'success');
    }
}

// 清空图片列表
function clearImageList() {
    if (selectedImages.length === 0) {
        showMessage('没有图片需要清除', 'info');
        return;
    }
    
    if (confirm('确定要清空所有图片吗？这将删除所有图片和相关的文字、二维码数据。')) {
        selectedImages = [];
        globalTextObjects = []; // 清空全局文字对象
        previewTextObject = null; // 重置预览文字对象
        userTextStyle = null; // 重置用户文字样式
        currentImageIndex = 0;
        canvas.clear();
        updateImageList();
        updateImageCounter();
        updateImageGrid();
        clearUploadResults();
        
        // 重置界面元素
        document.getElementById('textInput').value = '';
        // 移除自动应用文字效果选项，因为已禁用该功能
        // document.getElementById('autoApplyText').checked = false;
        // 移除应用按钮相关代码
        // document.getElementById('applyToAllBtn').style.display = 'none';
        
        showMessage('图片列表已清空，所有文字状态已重置', 'success');
    }
}

// 清理所有文字对象
function clearAllTexts() {
    if (confirm('确定要清除所有文字吗？这将删除当前画布和所有图片上的文字效果。')) {
        // 清除画布上的所有文字
        const textObjects = canvas.getObjects().filter(obj => obj.type === 'text');
        textObjects.forEach(textObj => canvas.remove(textObj));
        
        // 清空全局文字对象数组
        globalTextObjects = [];
        
        // 重置预览文字对象
        previewTextObject = null;
        
        // 重置用户文字样式
        userTextStyle = null;
        
        // 清除所有图片的文字状态
        selectedImages.forEach(imageInfo => {
            imageInfo.textState = [];
            imageInfo.processedWithText = false;
        });
        
        // 清空文字输入框
        document.getElementById('textInput').value = '';
        
        // 移除应用按钮相关代码
        // document.getElementById('applyToAllBtn').style.display = 'none';
        
        canvas.renderAll();
        showMessage('所有文字已清除', 'success');
    }
}

// 显示/隐藏进度条
function showProgress(show) {
    document.getElementById('progressContainer').style.display = show ? 'block' : 'none';
}

// 更新进度
function updateProgress(percent, text) {
    document.getElementById('progressFill').style.width = percent + '%';
    document.getElementById('progressText').textContent = text;
}

// 显示状态消息
function showMessage(message, type) {
    const messageEl = document.getElementById('statusMessage');
    messageEl.textContent = message;
    messageEl.className = 'status-message status-' + type;
    messageEl.style.display = 'block';
    
    setTimeout(() => {
        messageEl.style.display = 'none';
    }, 3000);
}

// 保存二维码
function saveQRCode() {
    const qrCodeImg = document.getElementById('qrCode');
    if (!qrCodeImg.src || qrCodeImg.style.display === 'none') {
        showMessage('没有可保存的二维码', 'error');
        return;
    }
    
    // 创建canvas来转换图片
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // 创建一个新的Image对象
    const img = new Image();
    img.crossOrigin = 'anonymous'; // 处理跨域问题
    
    img.onload = function() {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0);
        
        // 转换为数据URL
        const dataURL = canvas.toDataURL('image/png');
        
        // 创建下载链接
        const link = document.createElement('a');
        link.download = 'qrcode_' + Date.now() + '.png';
        link.href = dataURL;
        link.click();
        
        showMessage('二维码已保存', 'success');
    };
    
    img.onerror = function() {
        showMessage('保存二维码失败', 'error');
    };
    
    img.src = qrCodeImg.src;
}

// 将二维码添加到画布上
async function addQRCodeToCanvas(url, filename) {
    try {
        console.log(`为 ${filename} 生成二维码并添加到画布，URL:`, url);
        
        if (!url || url.trim() === '') {
            showMessage('图片链接为空，无法生成二维码', 'error');
            return;
        }
        
        let qrDataURL;
        
        // 生成二维码
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.GenerateQRCode) {
            console.log('使用Go方法生成二维码');
            qrDataURL = await window.go.main.App.GenerateQRCode(url);
        } else {
            // 备用方案：使用在线API
            console.log('使用在线API生成二维码');
            qrDataURL = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(url);
        }
        
        // 将二维码添加到画布
        fabric.Image.fromURL(qrDataURL, function(qrImg) {
            // 计算二维码大小
            const qrSize = Math.max(60, Math.min(120, Math.min(canvas.width, canvas.height) * 0.08));
            
            // 使用全局位置，如果没有设置则使用默认位置（右下角）
            let qrLeft = globalQRCodePosition.left;
            let qrTop = globalQRCodePosition.top;
            let qrScaleX, qrScaleY;
            
            // 如果全局位置为初始值(0,0)，使用默认的右下角位置
            if (qrLeft === 0 && qrTop === 0) {
                qrLeft = canvas.width - qrSize - 20;
                qrTop = canvas.height - qrSize - 20;
                console.log(`📍 使用默认位置: (${Math.round(qrLeft)}, ${Math.round(qrTop)})`);
            } else {
                console.log(`📍 使用全局位置: (${Math.round(qrLeft)}, ${Math.round(qrTop)})`);
            }
            
            // 确定二维码尺寸
            if (globalQRCodePosition.scaleX && globalQRCodePosition.scaleY && 
                (globalQRCodePosition.scaleX !== 1 || globalQRCodePosition.scaleY !== 1)) {
                // 使用全局尺寸
                qrScaleX = globalQRCodePosition.scaleX;
                qrScaleY = globalQRCodePosition.scaleY;
                console.log(`📏 使用全局尺寸: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
            } else {
                // 使用默认尺寸
                qrScaleX = qrSize / qrImg.width;
                qrScaleY = qrSize / qrImg.height;
                console.log(`📏 使用默认尺寸: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
            }
            
            qrImg.set({
                left: qrLeft,
                top: qrTop,
                scaleX: qrScaleX,
                scaleY: qrScaleY,
                selectable: true,
                moveable: true,
                hasControls: true,
                hasBorders: true,
                cornerSize: 8,
                transparentCorners: false,
                cornerColor: '#28a745',
                borderColor: '#28a745'
            });
            
            // 添加标识
            qrImg.set('type', 'qrcode');
            qrImg.set('originalUrl', url);
            qrImg.set('linkedFilename', filename);
            qrImg.set('timestamp', Date.now());
            qrImg.set('autoGenerated', true); // 标记为自动生成的二维码
            
            canvas.add(qrImg);
            
            // 如果这是第一个二维码且全局位置未设置，更新全局位置和尺寸
            if (globalQRCodePosition.left === 0 && globalQRCodePosition.top === 0) {
                globalQRCodePosition = {
                    left: qrLeft,
                    top: qrTop,
                    scaleX: qrScaleX,
                    scaleY: qrScaleY
                };
                console.log(`📍 设置初始全局二维码位置和尺寸: (${Math.round(qrLeft)}, ${Math.round(qrTop)}) 缩放: (${qrScaleX.toFixed(2)}, ${qrScaleY.toFixed(2)})`);
            }
            
            canvas.setActiveObject(qrImg);
            canvas.renderAll();
            
            showMessage(`${filename} 的二维码已添加到画布，可以拖动调整位置`, 'success');
        }, {
            crossOrigin: 'anonymous'
        });
        
    } catch (error) {
        console.error('添加二维码到画布时出错:', error);
        showMessage('添加二维码失败: ' + error.message, 'error');
    }
}

// 上传当前画布
async function uploadCanvas() {
    try {
        if (canvas.getObjects().length === 0) {
            showMessage('画布为空，请先添加内容', 'error');
            return;
        }

        // 确保没有选中的对象边框显示在上传的图片中
        canvas.discardActiveObject();
        canvas.renderAll();

        // 🎯 使用原图尺寸的高分辨率Canvas
        const canvasData = await createHighResolutionCanvas();
        
        showMessage('正在上传画布...', 'info');
        
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.UploadImageData) {
            const result = await window.go.main.App.UploadImageData(canvasData, '画布.png');
            console.log('画布上传结果:', result);
            
            // 检查URL字段，可能是大小写问题
            const uploadUrl = result.URL || result.url || result.Url;
            
            if (uploadUrl) {
                // 获取画布内容摘要
                const objects = canvas.getObjects();
                const qrCodes = objects.filter(obj => obj.type === 'qrcode');
                const texts = objects.filter(obj => obj.type === 'text');
                
                let filename = '合成图片';
                if (qrCodes.length > 0 || texts.length > 0) {
                    filename += ` (包含${qrCodes.length}个二维码${texts.length > 0 ? '和' + texts.length + '个文字' : ''})`;
                }
                
                // 添加到上传结果列表
                addUploadResult(filename, uploadUrl, true);
                showUploadResults(true);
                
                showMessage('画布上传成功！链接已添加到结果列表', 'success');
                
                // 更新文本输入框为上传链接，方便生成二维码
                document.getElementById('textInput').value = uploadUrl;
                
            } else {
                console.error('上传结果中没有找到URL字段:', result);
                showMessage('上传成功但未返回链接', 'error');
            }
        } else {
            showMessage('上传功能不可用，请检查应用连接', 'error');
        }
        
    } catch (error) {
        console.error('上传画布时出错:', error);
        showMessage('上传失败: ' + error.message, 'error');
    }
}

// 快速生成画布二维码
async function quickGenerateCanvasQR() {
    try {
        if (canvas.getObjects().length === 0) {
            showMessage('画布为空，请先添加内容', 'error');
            return;
        }

        showMessage('正在上传画布并生成二维码...', 'info');

        // 确保没有选中的对象边框显示在上传的图片中
        canvas.discardActiveObject();
        canvas.renderAll();

        // 🎯 使用原图尺寸的高分辨率Canvas
        const canvasData = await createHighResolutionCanvas();
        
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.UploadImageData) {
            const result = await window.go.main.App.UploadImageData(canvasData, '快速画布.png');
            console.log('画布上传结果:', result);
            
            // 检查URL字段，可能是大小写问题
            const uploadUrl = result.URL || result.url || result.Url;
            
            if (uploadUrl) {
                // 生成二维码
                let qrDataURL;
                if (window.go && window.go.main && window.go.main.App && window.go.main.App.GenerateQRCode) {
                    qrDataURL = await window.go.main.App.GenerateQRCode(uploadUrl);
                } else {
                    qrDataURL = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(uploadUrl);
                }
                
                // 将二维码添加到画布上
                fabric.Image.fromURL(qrDataURL, function(qrImg) {
                    // 设置二维码的初始属性
                    qrImg.set({
                        left: canvas.width - 120,  // 放在右上角
                        top: 20,
                        scaleX: 0.4,  // 稍微小一点
                        scaleY: 0.4,
                        selectable: true,
                        moveable: true,
                        hasControls: true,
                        hasBorders: true,
                        cornerSize: 10,
                        transparentCorners: false,
                        cornerColor: '#28a745',
                        borderColor: '#28a745'
                    });
                    
                    // 添加标识
                    qrImg.set('type', 'qrcode');
                    qrImg.set('originalContent', uploadUrl);
                    qrImg.set('timestamp', Date.now());
                    qrImg.set('isCanvasQR', true); // 标记为画布二维码
                    
                    canvas.add(qrImg);
                    
                    // 如果这是第一个二维码且全局位置未设置，更新全局位置
                    if (globalQRCodePosition.left === 0 && globalQRCodePosition.top === 0) {
                        globalQRCodePosition = {
                            left: qrImg.left,
                            top: qrImg.top,
                            scaleX: qrImg.scaleX,
                            scaleY: qrImg.scaleY
                        };
                        console.log(`📍 设置初始全局二维码位置和尺寸: (${Math.round(qrImg.left)}, ${Math.round(qrImg.top)}) 缩放: (${qrImg.scaleX.toFixed(2)}, ${qrImg.scaleY.toFixed(2)})`);
                    }
                    
                    canvas.setActiveObject(qrImg);
                    canvas.renderAll();
                    
                    showMessage('画布已上传，二维码已添加到画布上！', 'success');
                    
                    // 也添加到上传结果列表
                    addUploadResult('当前画布', uploadUrl, true);
                    showUploadResults(true);
                    
                }, {
                    crossOrigin: 'anonymous'
                });
                
            } else {
                showMessage('上传成功但未返回链接', 'error');
            }
        } else {
            showMessage('上传功能不可用，请检查应用连接', 'error');
        }
        
    } catch (error) {
        console.error('快速生成画布二维码时出错:', error);
        showMessage('操作失败: ' + error.message, 'error');
    }
}

// 为当前画布添加二维码（右下角）
async function addQRCodeToCurrentCanvas(uploadUrl, filename) {
    try {
        console.log(`为当前画布添加二维码，URL: ${uploadUrl}`);
        
        // 生成二维码
        let qrDataURL;
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.GenerateQRCode) {
            qrDataURL = await window.go.main.App.GenerateQRCode(uploadUrl);
        } else {
            qrDataURL = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(uploadUrl);
        }
        
        // 将二维码添加到当前画布的右下角
        fabric.Image.fromURL(qrDataURL, function(qrImg) {
            // 计算二维码大小（画布的8%，最小60px，最大120px）
            const qrSize = Math.max(60, Math.min(120, Math.min(canvas.width, canvas.height) * 0.08));
            
            // 使用全局位置，如果没有设置则使用默认位置（右下角）
            let qrLeft = globalQRCodePosition.left;
            let qrTop = globalQRCodePosition.top;
            let qrScaleX, qrScaleY;
            
            // 如果全局位置为初始值(0,0)，使用默认的右下角位置
            if (qrLeft === 0 && qrTop === 0) {
                qrLeft = canvas.width - qrSize - 20;
                qrTop = canvas.height - qrSize - 20;
                console.log(`📍 使用默认位置: (${Math.round(qrLeft)}, ${Math.round(qrTop)})`);
            } else {
                console.log(`📍 使用全局位置: (${Math.round(qrLeft)}, ${Math.round(qrTop)})`);
            }
            
            // 确定二维码尺寸
            if (globalQRCodePosition.scaleX && globalQRCodePosition.scaleY && 
                (globalQRCodePosition.scaleX !== 1 || globalQRCodePosition.scaleY !== 1)) {
                // 使用全局尺寸
                qrScaleX = globalQRCodePosition.scaleX;
                qrScaleY = globalQRCodePosition.scaleY;
                console.log(`📏 使用全局尺寸: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
            } else {
                // 使用默认尺寸
                qrScaleX = qrSize / qrImg.width;
                qrScaleY = qrSize / qrImg.height;
                console.log(`📏 使用默认尺寸: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
            }
            
            qrImg.set({
                left: qrLeft,
                top: qrTop,
                scaleX: qrScaleX,
                scaleY: qrScaleY,
                selectable: true,
                moveable: true,
                hasControls: true,
                hasBorders: true,
                cornerSize: 8,
                transparentCorners: false,
                cornerColor: '#28a745',
                borderColor: '#28a745'
            });
            
            // 添加标识
            qrImg.set('type', 'qrcode');
            qrImg.set('originalContent', uploadUrl);
            qrImg.set('linkedFilename', filename);
            qrImg.set('timestamp', Date.now());
            qrImg.set('autoGenerated', true); // 标记为自动生成的二维码
            
            // 添加二维码到画布
            canvas.add(qrImg);
            
            // 测试二维码对象的属性
            console.log(`📍 二维码对象属性测试:`, {
                type: qrImg.type,
                selectable: qrImg.selectable,
                moveable: qrImg.moveable,
                hasControls: qrImg.hasControls,
                hasBorders: qrImg.hasBorders,
                linkedFilename: qrImg.linkedFilename
            });
            
            // 如果这是第一个二维码且全局位置未设置，更新全局位置和尺寸
            if (globalQRCodePosition.left === 0 && globalQRCodePosition.top === 0) {
                globalQRCodePosition = {
                    left: qrLeft,
                    top: qrTop,
                    scaleX: qrScaleX,
                    scaleY: qrScaleY
                };
                console.log(`📍 设置初始全局二维码位置和尺寸: (${Math.round(qrLeft)}, ${Math.round(qrTop)}) 缩放: (${qrScaleX.toFixed(2)}, ${qrScaleY.toFixed(2)})`);
            }
            
            canvas.setActiveObject(qrImg);
            canvas.renderAll();
            
            showMessage(`${filename} 的二维码已添加到画布，可以拖动调整位置`, 'success');
        }, {
            crossOrigin: 'anonymous'
        });
        
    } catch (error) {
        console.error('添加二维码到画布时出错:', error);
        showMessage('添加二维码失败: ' + error.message, 'error');
    }
}

// 自动为图片添加二维码到右下角
async function autoAddQRCodeToImage(imageInfo, uploadUrl) {
    try {
        console.log(`为 ${imageInfo.name} 自动生成二维码，URL:`, uploadUrl);
        
        // 生成二维码
        let qrDataURL;
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.GenerateQRCode) {
            qrDataURL = await window.go.main.App.GenerateQRCode(uploadUrl);
        } else {
            qrDataURL = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(uploadUrl);
        }
        
        // 生成带二维码的最终图片
        const finalImageData = await addQRCodeToImageData(imageInfo.data, qrDataURL);
        
        // 重新上传带二维码的版本
        const finalResult = await window.go.main.App.UploadImageData(finalImageData, `${imageInfo.name}_二维码版本.png`);
        const finalUploadUrl = finalResult.URL || finalResult.url || finalResult.Url;
        
        if (finalUploadUrl) {
            console.log(`最终版本上传成功: ${imageInfo.name} -> ${finalUploadUrl}`);
            console.log(`二维码内容: ${uploadUrl}`);
            return finalUploadUrl;
        } else {
            console.error('最终版本上传失败:', finalResult);
            return null;
        }
        
    } catch (error) {
        console.error(`为 ${imageInfo.name} 添加二维码时出错:`, error);
        return null;
    }
}

// 将二维码添加到图片数据（右下角）
async function addQRCodeToImageData(imageData, qrDataURL) {
    return new Promise((resolve, reject) => {
        // 创建临时画布
        const tempCanvas = new fabric.Canvas(document.createElement('canvas'));
        
        fabric.Image.fromURL(imageData, function(img) {
            // 设置画布大小为图片大小
            tempCanvas.setWidth(img.width);
            tempCanvas.setHeight(img.height);
            
            // 添加原图片到画布
            img.set({
                left: 0,
                top: 0,
                selectable: false
            });
            tempCanvas.add(img);
            
            // 添加二维码
            fabric.Image.fromURL(qrDataURL, function(qrImg) {
                // 计算二维码大小（图片尺寸的8%，最小80px，最大150px）
                const qrSize = Math.max(80, Math.min(150, Math.min(img.width, img.height) * 0.08));
                
                qrImg.set({
                    left: img.width - qrSize - 20, // 右下角，距离边缘20px
                    top: img.height - qrSize - 20,
                    scaleX: qrSize / qrImg.width,
                    scaleY: qrSize / qrImg.height,
                    selectable: false
                });
                
                // 添加二维码
                tempCanvas.add(qrImg);
                tempCanvas.renderAll();
                
                // 转换为数据URL
                const processedData = tempCanvas.toDataURL('image/png', 1.0);
                
                // 清理临时画布
                tempCanvas.dispose();
                
                resolve(processedData);
            }, {
                crossOrigin: 'anonymous'
            });
        });
    });
}

// 为每张图片生成二维码数据
async function generateQRCodeForImage(uploadUrl) {
    try {
        console.log(`📍 generateQRCodeForImage 开始处理URL: "${uploadUrl}"`);
        console.log(`📍 URL长度: ${uploadUrl.length}, URL类型: ${typeof uploadUrl}`);
        
        let qrDataURL;
        if (window.go && window.go.main && window.go.main.App && window.go.main.App.GenerateQRCode) {
            console.log(`📍 调用Go方法生成二维码，传入参数: "${uploadUrl}"`);
            qrDataURL = await window.go.main.App.GenerateQRCode(uploadUrl);
            console.log(`📍 Go方法返回的二维码数据长度: ${qrDataURL.length}`);
            console.log(`📍 二维码数据前50字符: ${qrDataURL.substring(0, 50)}`);
            console.log(`📍 二维码数据后50字符: ${qrDataURL.substring(qrDataURL.length - 50)}`);
        } else {
            console.log(`📍 使用在线API生成二维码`);
            qrDataURL = 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=' + encodeURIComponent(uploadUrl);
        }
        
        console.log(`📍 generateQRCodeForImage 完成，返回结果长度: ${qrDataURL ? qrDataURL.length : 'null'}`);
        return qrDataURL;
    } catch (error) {
        console.error('生成二维码失败:', error);
        return null;
    }
}

// 从保存的数据将二维码添加到画布
function addQRCodeToCanvasFromData(qrDataURL, uploadUrl, filename, position = null) {
    console.log(`开始添加二维码到画布: ${filename}`);
    console.log('二维码数据URL:', qrDataURL.substring(0, 50) + '...');
    console.log('对应的上传URL:', uploadUrl);
    console.log('指定位置:', position);
    
    // 检查是否已存在相同文件名的二维码
    const existingQR = canvas.getObjects().find(obj => 
        obj.type === 'qrcode' && obj.linkedFilename === filename
    );
    
    if (existingQR) {
        console.log(`✓ 二维码已存在，跳过添加: ${filename}`);
        return;
    }
    
    // 将二维码添加到当前画布
    fabric.Image.fromURL(qrDataURL, function(qrImg) {
        console.log(`✓ 二维码图片加载成功: ${filename}`);
        
        // 计算二维码大小（画布的8%，最小60px，最大120px）
        const qrSize = Math.max(60, Math.min(120, Math.min(canvas.width, canvas.height) * 0.08));
        
        // 确定二维码位置
        let qrLeft, qrTop, qrScaleX, qrScaleY;
        
        // 优先使用全局位置，否则使用默认位置（右下角）
        if (globalQRCodePosition.left !== 0 || globalQRCodePosition.top !== 0) {
            qrLeft = globalQRCodePosition.left;
            qrTop = globalQRCodePosition.top;
            console.log(`📍 使用全局位置: (${Math.round(qrLeft)}, ${Math.round(qrTop)})`);
        } else {
            qrLeft = canvas.width - qrSize - 20;
            qrTop = canvas.height - qrSize - 20;
            console.log(`📍 使用默认位置: (${Math.round(qrLeft)}, ${Math.round(qrTop)})`);
        }
        
        // 确定二维码尺寸
        if (globalQRCodePosition.scaleX && globalQRCodePosition.scaleY && 
            (globalQRCodePosition.scaleX !== 1 || globalQRCodePosition.scaleY !== 1)) {
            // 使用全局尺寸
            qrScaleX = globalQRCodePosition.scaleX;
            qrScaleY = globalQRCodePosition.scaleY;
            console.log(`📏 使用全局尺寸: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
        } else {
            // 使用默认尺寸
            qrScaleX = qrSize / qrImg.width;
            qrScaleY = qrSize / qrImg.height;
            console.log(`📏 使用默认尺寸: ${qrScaleX.toFixed(2)}x${qrScaleY.toFixed(2)}`);
        }
        
        qrImg.set({
            left: qrLeft,
            top: qrTop,
            scaleX: qrScaleX,
            scaleY: qrScaleY,
            selectable: true,
            moveable: true,
            hasControls: true,
            hasBorders: true,
            cornerSize: 8,
            transparentCorners: false,
            cornerColor: '#28a745',
            borderColor: '#28a745'
        });
        
        // 添加标识
        qrImg.set('type', 'qrcode');
        qrImg.set('originalUrl', uploadUrl);
        qrImg.set('linkedFilename', filename);
        qrImg.set('timestamp', Date.now());
        qrImg.set('autoGenerated', true); // 标记为自动生成的二维码
        
        canvas.add(qrImg);
        
        // 如果这是第一个二维码且全局位置未设置，更新全局位置和尺寸
        if (globalQRCodePosition.left === 0 && globalQRCodePosition.top === 0) {
            globalQRCodePosition = {
                left: qrLeft,
                top: qrTop,
                scaleX: qrScaleX,
                scaleY: qrScaleY
            };
            console.log(`📍 设置初始全局二维码位置和尺寸: (${Math.round(qrLeft)}, ${Math.round(qrTop)}) 缩放: (${qrScaleX.toFixed(2)}, ${qrScaleY.toFixed(2)})`);
        }
        
        // 清理重复的二维码
        cleanupDuplicateQRCodes();
        
        canvas.renderAll();
        
        console.log(`✓ 二维码已添加到画布: ${filename}，位置: (${Math.round(qrLeft)}, ${Math.round(qrTop)})`);
    }, {
        crossOrigin: 'anonymous'
    });
}

// 更新图片网格
function updateImageGrid() {
    const imageGrid = document.getElementById('imageGrid');
    imageGrid.innerHTML = '';
    
    selectedImages.forEach((imageInfo, index) => {
        const gridItem = document.createElement('div');
        gridItem.className = `grid-item ${index === currentImageIndex ? 'active' : ''}`;
        
        // 图片
        const img = document.createElement('img');
        img.src = imageInfo.data;
        // 移除alt属性避免显示图片名字
        // img.alt = imageInfo.name;
        // 明确设置title为空，避免显示工具提示
        img.title = '';
        img.setAttribute('data-filename', ''); // 清除任何可能的数据属性
        
        // 状态图标
        const statusDiv = document.createElement('div');
        statusDiv.className = 'grid-item-status';
        
        if (imageInfo.isUploaded) {
            const uploadedIcon = document.createElement('div');
            uploadedIcon.className = 'status-icon status-uploaded';
            uploadedIcon.textContent = '✓';
            uploadedIcon.title = '已上传';
            statusDiv.appendChild(uploadedIcon);
        }
        
        if (imageInfo.hasQRCode) {
            const qrIcon = document.createElement('div');
            qrIcon.className = 'status-icon status-qr';
            qrIcon.textContent = '⊞';
            qrIcon.title = '有二维码';
            statusDiv.appendChild(qrIcon);
        }
        
        if (imageInfo.processedWithText) {
            const textIcon = document.createElement('div');
            textIcon.className = 'status-icon status-text';
            textIcon.textContent = 'T';
            textIcon.title = '已处理文字';
            statusDiv.appendChild(textIcon);
        }
        
        // 点击事件
        gridItem.onclick = function() {
            loadImageToCanvas(index);
        };
        
        gridItem.appendChild(img);
        gridItem.appendChild(statusDiv);
        imageGrid.appendChild(gridItem);
    });
}

// 更新选中文字的字体
function updateSelectedTextFont(fontFamily) {
    const activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type === 'text') {
        activeObject.set('fontFamily', fontFamily);
        canvas.renderAll();
        updateUserTextStyle(activeObject);
    }
}

// 更新选中文字的颜色
function updateSelectedTextColor(color) {
    const activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type === 'text') {
        activeObject.set('fill', color);
        canvas.renderAll();
        updateUserTextStyle(activeObject);
    }
}

// 更新选中文字的大小
function updateSelectedTextSize(size) {
    const activeObject = canvas.getActiveObject();
    if (activeObject && activeObject.type === 'text') {
        activeObject.set('fontSize', size);
        canvas.renderAll();
        updateUserTextStyle(activeObject);
    }
}

// 移除自动文字预览功能，因为已禁用自动应用文字效果
function updateAutoTextPreview() {
    // 该功能已被禁用，因为自动应用文字效果功能已移除
    // const autoApplyEnabled = document.getElementById('autoApplyText').checked;
    // const textContent = document.getElementById('textInput').value.trim();
    
    // if (autoApplyEnabled && textContent && currentImageIndex >= 0) {
    //     // 重新加载当前图片以应用更新的文字效果
    //     loadImageToCanvas(currentImageIndex);
    // }
}

// 更新预览文字内容
function updatePreviewText(textContent) {
    // 移除预览文字功能 - 现在只有点击"添加文字"后才显示文字
    return;
    
    // 以下代码被注释，因为不再需要自动预览功能
    /*
    if (!textContent) {
        // 如果文字为空，移除预览文字
        if (previewTextObject) {
            canvas.remove(previewTextObject);
            previewTextObject = null;
            canvas.renderAll();
        }
        return;
    }
    
    // 检查是否已有相同内容的正式文字对象，避免重复预览
    const existingTexts = canvas.getObjects().filter(obj => 
        obj.type === 'text' && obj.text === textContent && !obj.isPreviewText
    );
    
    if (existingTexts.length > 0) {
        // 如果已有相同文字，移除预览文字
        if (previewTextObject) {
            canvas.remove(previewTextObject);
            previewTextObject = null;
            canvas.renderAll();
        }
        return;
    }
    
    // 获取当前样式设置
    const fontFamily = document.getElementById('fontSelect').value;
    const textColor = document.getElementById('textColor').value;
    const fontSize = parseInt(document.getElementById('fontSize').value);
    
    if (previewTextObject) {
        // 更新现有预览文字
        previewTextObject.set('text', textContent);
        previewTextObject.set('fontFamily', fontFamily);
        previewTextObject.set('fill', textColor);
        previewTextObject.set('fontSize', fontSize);
        previewTextObject.set('stroke', textColor === '#000000' ? '#ffffff' : '#000000');
    } else {
        // 创建新的预览文字对象
        previewTextObject = new fabric.Text(textContent, {
            left: canvas.width / 2,
            top: canvas.height / 2,
            originX: 'center',
            originY: 'center',
            fontFamily: fontFamily,
            fontSize: fontSize,
            fill: textColor,
            selectable: true,
            moveable: true,
            hasControls: true,
            hasBorders: true,
            cornerSize: 10,
            transparentCorners: false,
            cornerColor: '#007bff',
            borderColor: '#007bff',
            // 添加文字描边提高可读性
            stroke: textColor === '#000000' ? '#ffffff' : '#000000',
            strokeWidth: 1
        });
        
        // 添加标识
        previewTextObject.set('isPreviewText', true);
        
        canvas.add(previewTextObject);
        canvas.setActiveObject(previewTextObject);
        
        // 为预览文字对象添加事件监听器
        addTextEventListeners(previewTextObject);
    }
    
    canvas.renderAll();
    
    // 检查是否有选中的图片可以应用文字效果
    const selectedCount = selectedImages.filter(img => img.selected).length;
    if (selectedCount > 1) {
        document.getElementById('applyToAllBtn').style.display = 'inline-block';
    }
    */
}

// 更新预览文字的字体
function updatePreviewTextFont(fontFamily) {
    if (previewTextObject) {
        previewTextObject.set('fontFamily', fontFamily);
        canvas.renderAll();
        updateUserTextStyle(previewTextObject);
    }
}

// 更新预览文字的颜色
function updatePreviewTextColor(color) {
    if (previewTextObject) {
        previewTextObject.set('fill', color);
        canvas.renderAll();
        updateUserTextStyle(previewTextObject);
    }
}

// 更新预览文字的大小
function updatePreviewTextSize(size) {
    if (previewTextObject) {
        previewTextObject.set('fontSize', size);
        canvas.renderAll();
        updateUserTextStyle(previewTextObject);
    }
}

// 更新全局文字对象
function updateGlobalTextObject(textObj) {
    if (textObj.globalTextId) {
        const globalText = globalTextObjects.find(gt => gt.id === textObj.globalTextId);
        if (globalText) {
            globalText.text = textObj.text;
            globalText.left = textObj.left;
            globalText.top = textObj.top;
            globalText.originX = textObj.originX;
            globalText.originY = textObj.originY;
            globalText.fontFamily = textObj.fontFamily;
            globalText.fontSize = textObj.fontSize;
            globalText.fill = textObj.fill;
            globalText.scaleX = textObj.scaleX || 1;
            globalText.scaleY = textObj.scaleY || 1;
            globalText.angle = textObj.angle || 0;
        }
    }
}

// 同步二维码位置到所有图片的状态
function syncQRCodePositionToAllImages() {
    console.log(`📍 开始同步二维码位置和大小到所有图片...`);
    
    selectedImages.forEach((imageInfo, index) => {
        // 如果图片有二维码相关信息（已上传或有二维码数据），则同步位置
        if (imageInfo.hasQRCode || imageInfo.qrCodeData || imageInfo.isUploaded) {
            // 如果还没有qrCodeState，创建一个
            if (!imageInfo.qrCodeState) {
                imageInfo.qrCodeState = [];
            }
            
            // 如果qrCodeState为空但图片有二维码，创建一个基本状态
            if (imageInfo.qrCodeState.length === 0 && (imageInfo.hasQRCode || imageInfo.qrCodeData)) {
                imageInfo.qrCodeState.push({
                    left: globalQRCodePosition.left,
                    top: globalQRCodePosition.top,
                    scaleX: globalQRCodePosition.scaleX || 1,
                    scaleY: globalQRCodePosition.scaleY || 1,
                    angle: 0,
                    originalContent: imageInfo.uploadedUrl || '',
                    linkedFilename: imageInfo.name,
                    timestamp: Date.now(),
                    autoGenerated: true
                });
            } else {
                // 更新现有的二维码状态中的位置和大小信息
                imageInfo.qrCodeState.forEach(qrState => {
                    qrState.left = globalQRCodePosition.left;
                    qrState.top = globalQRCodePosition.top;
                    qrState.scaleX = globalQRCodePosition.scaleX || 1;
                    qrState.scaleY = globalQRCodePosition.scaleY || 1;
                });
            }
            
            console.log(`📍 已同步二维码位置和大小到图片: ${imageInfo.name}，新位置: (${Math.round(globalQRCodePosition.left)}, ${Math.round(globalQRCodePosition.top)})，新大小: ${globalQRCodePosition.scaleX.toFixed(2)}x${globalQRCodePosition.scaleY.toFixed(2)}`);
        }
    });
    
    console.log(`📍 二维码位置和大小同步完成，共处理 ${selectedImages.length} 张图片`);
}

// 清理画布上重复的二维码
function cleanupDuplicateQRCodes() {
    const qrObjects = canvas.getObjects().filter(obj => obj.type === 'qrcode');
    const seenQRs = new Map();
    const toRemove = [];
    
    qrObjects.forEach(qrObj => {
        const key = qrObj.linkedFilename || 'unknown';
        if (seenQRs.has(key)) {
            // 发现重复二维码，保留最新的，删除旧的
            const existing = seenQRs.get(key);
            if (qrObj.timestamp && existing.timestamp) {
                if (qrObj.timestamp > existing.timestamp) {
                    // 新的更新，删除旧的
                    toRemove.push(existing);
                    seenQRs.set(key, qrObj);
                } else {
                    // 旧的，删除当前的
                    toRemove.push(qrObj);
                }
            } else {
                // 没有时间戳，删除当前的
                toRemove.push(qrObj);
            }
        } else {
            seenQRs.set(key, qrObj);
        }
    });
    
    // 删除重复的二维码对象
    toRemove.forEach(qrObj => {
        canvas.remove(qrObj);
    });
    
    if (toRemove.length > 0) {
        canvas.renderAll();
        console.log(`📍 清理了 ${toRemove.length} 个重复的二维码对象`);
    }
}

// 同步二维码位置到其他图片的状态（排除当前图片）
function syncQRCodePositionToOtherImages() {
    console.log(`📍 开始同步二维码位置和大小到其他图片...（排除当前图片 ${currentImageIndex}）`);
    console.log(`📍 全局二维码位置: (${Math.round(globalQRCodePosition.left)}, ${Math.round(globalQRCodePosition.top)})，大小: ${globalQRCodePosition.scaleX.toFixed(2)}x${globalQRCodePosition.scaleY.toFixed(2)}`);
    
    let syncedCount = 0;
    
    selectedImages.forEach((imageInfo, index) => {
        // 跳过当前图片
        if (index === currentImageIndex) {
            console.log(`📍 跳过当前图片: ${imageInfo.name}`);
            return;
        }
        
        // 检查图片是否需要同步二维码位置
        const needsSync = imageInfo.hasQRCode || 
                         imageInfo.qrCodeData || 
                         imageInfo.isUploaded || 
                         (imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0);
        
        if (needsSync) {
            // 确保图片有qrCodeState数组
            if (!imageInfo.qrCodeState) {
                imageInfo.qrCodeState = [];
                console.log(`📍 为图片创建qrCodeState: ${imageInfo.name}`);
            }
            
            // 如果qrCodeState为空但图片应该有二维码，创建一个基本状态
            if (imageInfo.qrCodeState.length === 0) {
                const qrState = {
                    left: globalQRCodePosition.left,
                    top: globalQRCodePosition.top,
                    scaleX: globalQRCodePosition.scaleX || 1,
                    scaleY: globalQRCodePosition.scaleY || 1,
                    angle: 0,
                    originalContent: imageInfo.uploadedUrl || imageInfo.qrCodeData || '',
                    linkedFilename: imageInfo.name,
                    timestamp: Date.now(),
                    autoGenerated: true
                };
                imageInfo.qrCodeState.push(qrState);
                console.log(`📍 为图片创建新二维码状态: ${imageInfo.name}，位置: (${Math.round(qrState.left)}, ${Math.round(qrState.top)})，大小: ${qrState.scaleX}x${qrState.scaleY}`);
            } else {
                // 更新现有的二维码状态中的位置和大小信息
                imageInfo.qrCodeState.forEach((qrState, qrIndex) => {
                    const oldInfo = `(${Math.round(qrState.left)}, ${Math.round(qrState.top)}, ${qrState.scaleX}x${qrState.scaleY})`;
                    qrState.left = globalQRCodePosition.left;
                    qrState.top = globalQRCodePosition.top;
                    qrState.scaleX = globalQRCodePosition.scaleX || 1;
                    qrState.scaleY = globalQRCodePosition.scaleY || 1;
                    qrState.timestamp = Date.now(); // 更新时间戳
                    const newInfo = `(${Math.round(qrState.left)}, ${Math.round(qrState.top)}, ${qrState.scaleX}x${qrState.scaleY})`;
                    console.log(`📍 更新二维码状态 ${qrIndex + 1}：${imageInfo.name}，${oldInfo} -> ${newInfo}`);
                });
            }
            
            syncedCount++;
            console.log(`📍 已同步二维码位置和大小到图片: ${imageInfo.name}，二维码状态数量: ${imageInfo.qrCodeState.length}`);
        } else {
            console.log(`📍 跳过图片（无需二维码同步）: ${imageInfo.name}`);
        }
    });
    
    console.log(`📍 二维码位置和大小同步完成，共处理 ${syncedCount} 张其他图片`);
    
    // 验证同步结果
    const totalImagesWithQR = selectedImages.filter(img => 
        img.hasQRCode || img.qrCodeData || img.isUploaded || 
        (img.qrCodeState && img.qrCodeState.length > 0)
    ).length;
    console.log(`📍 总共有二维码的图片数量: ${totalImagesWithQR}，已同步: ${syncedCount + 1}`); // +1包括当前图片
}

// 强制同步所有图片的二维码位置（包括当前图片）
function forceQRCodePositionSync() {
    console.log(`📍 开始强制同步所有图片的二维码位置...`);
    console.log(`📍 全局二维码位置: (${Math.round(globalQRCodePosition.left)}, ${Math.round(globalQRCodePosition.top)})`);
    
    if (globalQRCodePosition.left === 0 && globalQRCodePosition.top === 0) {
        console.log(`📍 全局位置未设置，无法进行同步`);
        return;
    }
    
    let syncedCount = 0;
    
    selectedImages.forEach((imageInfo, index) => {
        // 检查图片是否应该有二维码
        const shouldHaveQR = imageInfo.hasQRCode || 
                           imageInfo.qrCodeData || 
                           imageInfo.isUploaded || 
                           (imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0);
        
        if (shouldHaveQR) {
            // 确保图片有qrCodeState数组
            if (!imageInfo.qrCodeState) {
                imageInfo.qrCodeState = [];
                console.log(`📍 为图片创建qrCodeState: ${imageInfo.name}`);
            }
            
            // 如果qrCodeState为空，创建一个新的状态
            if (imageInfo.qrCodeState.length === 0) {
                const qrState = {
                    left: globalQRCodePosition.left,
                    top: globalQRCodePosition.top,
                    scaleX: globalQRCodePosition.scaleX || 1,
                    scaleY: globalQRCodePosition.scaleY || 1,
                    angle: 0,
                    originalContent: imageInfo.uploadedUrl || imageInfo.qrCodeData || '',
                    linkedFilename: imageInfo.name,
                    timestamp: Date.now(),
                    autoGenerated: true
                };
                imageInfo.qrCodeState.push(qrState);
                console.log(`📍 为图片创建新二维码状态: ${imageInfo.name}，位置: (${Math.round(qrState.left)}, ${Math.round(qrState.top)})，大小: ${qrState.scaleX}x${qrState.scaleY}`);
            } else {
                // 强制更新所有现有的二维码状态
                imageInfo.qrCodeState.forEach((qrState, qrIndex) => {
                    const oldPosition = `(${Math.round(qrState.left)}, ${Math.round(qrState.top)}, ${qrState.scaleX}x${qrState.scaleY})`;
                    qrState.left = globalQRCodePosition.left;
                    qrState.top = globalQRCodePosition.top;
                    qrState.scaleX = globalQRCodePosition.scaleX || 1;
                    qrState.scaleY = globalQRCodePosition.scaleY || 1;
                    qrState.timestamp = Date.now();
                    const newPosition = `(${Math.round(qrState.left)}, ${Math.round(qrState.top)}, ${qrState.scaleX}x${qrState.scaleY})`;
                    console.log(`📍 强制更新二维码状态 ${qrIndex + 1}：${imageInfo.name}，${oldPosition} -> ${newPosition}`);
                });
            }
            
            syncedCount++;
        }
    });
    
    console.log(`📍 强制同步完成，共处理 ${syncedCount} 张图片`);
    
    // 如果当前画布上有二维码，也同步位置和大小
    const currentQRCodes = canvas.getObjects().filter(obj => obj.type === 'qrcode');
    if (currentQRCodes.length > 0) {
        console.log(`📍 同步当前画布上的 ${currentQRCodes.length} 个二维码位置和大小...`);
        currentQRCodes.forEach(qrCode => {
            const oldInfo = `(${Math.round(qrCode.left)}, ${Math.round(qrCode.top)}, ${qrCode.scaleX.toFixed(2)}x${qrCode.scaleY.toFixed(2)})`;
            qrCode.set({
                left: globalQRCodePosition.left,
                top: globalQRCodePosition.top,
                scaleX: globalQRCodePosition.scaleX || 1,
                scaleY: globalQRCodePosition.scaleY || 1
            });
            qrCode.setCoords();
            const newInfo = `(${Math.round(qrCode.left)}, ${Math.round(qrCode.top)}, ${qrCode.scaleX.toFixed(2)}x${qrCode.scaleY.toFixed(2)})`;
            console.log(`📍 同步画布二维码: ${qrCode.linkedFilename}，${oldInfo} -> ${newInfo}`);
        });
        canvas.renderAll();
    }
}

// 同步二维码大小到其他图片（排除当前图片）
function syncQRCodeSizeToOtherImages() {
    console.log(`📏 开始同步二维码大小到其他图片...（排除当前图片 ${currentImageIndex}）`);
    console.log(`📏 全局二维码大小: ${globalQRCodePosition.scaleX.toFixed(2)}x${globalQRCodePosition.scaleY.toFixed(2)}`);
    
    let syncedCount = 0;
    
    selectedImages.forEach((imageInfo, index) => {
        // 跳过当前图片
        if (index === currentImageIndex) {
            console.log(`📏 跳过当前图片: ${imageInfo.name}`);
            return;
        }
        
        // 检查图片是否需要同步二维码大小
        const needsSync = imageInfo.hasQRCode || 
                         imageInfo.qrCodeData || 
                         imageInfo.isUploaded || 
                         (imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0);
        
        if (needsSync) {
            // 确保图片有qrCodeState数组
            if (!imageInfo.qrCodeState) {
                imageInfo.qrCodeState = [];
                console.log(`📏 为图片创建qrCodeState: ${imageInfo.name}`);
            }
            
            // 如果qrCodeState为空但图片应该有二维码，创建一个基本状态
            if (imageInfo.qrCodeState.length === 0) {
                const qrState = {
                    left: globalQRCodePosition.left,
                    top: globalQRCodePosition.top,
                    scaleX: globalQRCodePosition.scaleX || 1,
                    scaleY: globalQRCodePosition.scaleY || 1,
                    angle: 0,
                    originalContent: imageInfo.uploadedUrl || imageInfo.qrCodeData || '',
                    linkedFilename: imageInfo.name,
                    timestamp: Date.now(),
                    autoGenerated: true
                };
                imageInfo.qrCodeState.push(qrState);
                console.log(`📏 为图片创建新二维码状态: ${imageInfo.name}，大小: ${qrState.scaleX}x${qrState.scaleY}`);
            } else {
                // 更新现有的二维码状态中的大小信息
                imageInfo.qrCodeState.forEach((qrState, qrIndex) => {
                    const oldSize = `${qrState.scaleX}x${qrState.scaleY}`;
                    qrState.scaleX = globalQRCodePosition.scaleX || 1;
                    qrState.scaleY = globalQRCodePosition.scaleY || 1;
                    qrState.timestamp = Date.now(); // 更新时间戳
                    const newSize = `${qrState.scaleX}x${qrState.scaleY}`;
                    console.log(`📏 更新二维码大小 ${qrIndex + 1}：${imageInfo.name}，${oldSize} -> ${newSize}`);
                });
            }
            
            syncedCount++;
            console.log(`📏 已同步二维码大小到图片: ${imageInfo.name}，二维码状态数量: ${imageInfo.qrCodeState.length}`);
        } else {
            console.log(`📏 跳过图片（无需二维码同步）: ${imageInfo.name}`);
        }
    });
    
    console.log(`📏 二维码大小同步完成，共处理 ${syncedCount} 张其他图片`);
}

// 测试连续移动功能
function testContinuousMovement() {
    console.log('📍 ===== 开始测试连续移动功能 =====');
    console.log(`📍 当前状态: isQRCodeMoving=${isQRCodeMoving}, draggedQRCode=${draggedQRCode ? draggedQRCode.linkedFilename : 'null'}`);
    console.log(`📍 全局二维码位置:`, globalQRCodePosition);
    console.log(`📍 原始位置映射大小: ${originalQRPositions.size}`);
    
    const qrCodes = canvas.getObjects().filter(obj => obj.type === 'qrcode');
    console.log(`📍 画布上二维码数量: ${qrCodes.length}`);
    qrCodes.forEach((qr, index) => {
        console.log(`📍 二维码 ${index + 1}: ${qr.linkedFilename}, 位置: (${Math.round(qr.left)}, ${Math.round(qr.top)})，大小: ${qr.scaleX.toFixed(2)}x${qr.scaleY.toFixed(2)}`);
    });
    
    console.log('📍 请尝试连续移动二维码，观察日志输出...');
    console.log('📍 ===== 测试开始 =====');
}

// 测试二维码大小同步功能
function testQRCodeSizeSync() {
    console.log('📏 ===== 开始测试二维码大小同步功能 =====');
    
    const qrCodes = canvas.getObjects().filter(obj => obj.type === 'qrcode');
    console.log(`📏 画布上二维码数量: ${qrCodes.length}`);
    
    if (qrCodes.length === 0) {
        console.log('📏 ❌ 画布上没有二维码，无法测试大小同步功能');
        console.log('📏 请先添加一些二维码后再测试');
        return;
    }
    
    console.log('📏 当前二维码状态:');
    qrCodes.forEach((qr, index) => {
        console.log(`📏 二维码 ${index + 1}: ${qr.linkedFilename}`);
        console.log(`   位置: (${Math.round(qr.left)}, ${Math.round(qr.top)})`);
        console.log(`   大小: ${qr.scaleX.toFixed(2)}x${qr.scaleY.toFixed(2)}`);
    });
    
    console.log(`📏 全局二维码状态: 位置(${Math.round(globalQRCodePosition.left)}, ${Math.round(globalQRCodePosition.top)})，大小${globalQRCodePosition.scaleX.toFixed(2)}x${globalQRCodePosition.scaleY.toFixed(2)}`);
    
    console.log('📏 图片状态中的二维码大小:');
    selectedImages.forEach((imageInfo, index) => {
        if (imageInfo.qrCodeState && imageInfo.qrCodeState.length > 0) {
            console.log(`📏 图片 ${index + 1}: ${imageInfo.name}`);
            imageInfo.qrCodeState.forEach((qrState, qrIndex) => {
                console.log(`   二维码 ${qrIndex + 1}: 位置(${Math.round(qrState.left)}, ${Math.round(qrState.top)})，大小${qrState.scaleX.toFixed(2)}x${qrState.scaleY.toFixed(2)}`);
            });
        }
    });
    
    console.log('📏 ===== 测试方法 =====');
    console.log('📏 1. 选中画布上的任意二维码');
    console.log('📏 2. 按住Shift键并拖拽二维码的角落控制点来缩放');
    console.log('📏 3. 观察其他二维码是否实时同步大小');
    console.log('📏 4. 切换到其他图片，查看二维码大小是否一致');
    console.log('📏 5. 可以运行 testImageSwitching() 来验证切换图片后的大小同步');
    console.log('📏 ===== 开始测试 =====');
}

// 强制同步二维码大小测试
function testForceSizeSync() {
    console.log('📏 ===== 开始强制同步二维码大小测试 =====');
    
    // 手动调用大小同步函数
    syncQRCodeSizeToOtherImages();
    
    console.log('📏 手动大小同步完成');
    console.log('📏 ===== 测试完成 =====');
}

console.log('📏 注册二维码大小同步测试函数:');
console.log('📏 - testQRCodeSizeSync() : 测试二维码大小同步功能');
console.log('📏 - testForceSizeSync() : 强制同步二维码大小');

console.log('📍 注册测试函数: testContinuousMovement() - 测试连续移动功能');

// 保存所有图片到文件夹
async function saveAllImages() {
    if (selectedImages.length === 0) {
        showMessage('没有图片可以保存', 'error');
        return;
    }

    try {
        // 检查浏览器是否支持目录选择
        if (!window.showDirectoryPicker) {
            // 降级方案：使用JSZip创建压缩包
            await saveAllImagesAsZip();
            return;
        }

        // 使用现代浏览器的目录选择API
        const directoryHandle = await window.showDirectoryPicker();
        
        showProgress(true);
        showMessage('开始保存所有图片...', 'info');
        
        const totalImages = selectedImages.length;
        let processedCount = 0;
        let successCount = 0;
        let failureCount = 0;
        
        const currentCanvasIndex = currentImageIndex;
        
        for (let i = 0; i < selectedImages.length; i++) {
            try {
                updateProgress((processedCount / totalImages) * 100, `正在处理第 ${processedCount + 1} / ${totalImages} 张图片...`);
                
                // 切换到当前图片
                await loadImageToCanvas(i);
                
                // 等待画布渲染完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 🎯 获取高分辨率画布数据
                const dataURL = await createHighResolutionCanvas();
                const response = await fetch(dataURL);
                const blob = await response.blob();
                
                // 生成文件名
                const imageInfo = selectedImages[i];
                const originalName = imageInfo.name;
                const nameWithoutExt = originalName.replace(/\.[^/.]+$/, "");
                const fileName = `${nameWithoutExt}_processed.png`;
                
                // 保存文件到选择的目录
                const fileHandle = await directoryHandle.getFileHandle(fileName, { create: true });
                const writable = await fileHandle.createWritable();
                await writable.write(blob);
                await writable.close();
                
                successCount++;
                console.log(`✅ 已保存: ${fileName}`);
                
            } catch (error) {
                console.error(`❌ 保存失败 - ${selectedImages[i].name}:`, error);
                failureCount++;
            }
            
            processedCount++;
        }
        
        // 恢复到原来的图片
        if (currentCanvasIndex >= 0 && currentCanvasIndex < selectedImages.length) {
            await loadImageToCanvas(currentCanvasIndex);
        }
        
        updateProgress(100, '保存完成');
        
        // 显示结果
        if (successCount === totalImages) {
            showMessage(`🎉 所有 ${successCount} 张图片保存成功！`, 'success');
        } else if (successCount > 0) {
            showMessage(`⚠️ 部分完成：${successCount} 张成功，${failureCount} 张失败`, 'warning');
        } else {
            showMessage(`❌ 保存失败，请检查文件夹权限`, 'error');
        }
        
        setTimeout(() => {
            showProgress(false);
        }, 2000);
        
    } catch (error) {
        console.error('保存所有图片时出错:', error);
        showProgress(false);
        
        if (error.name === 'AbortError') {
            showMessage('操作已取消', 'info');
        } else {
            showMessage('保存失败: ' + error.message, 'error');
        }
    }
}

// 备用方案：将所有图片保存为ZIP文件
async function saveAllImagesAsZip() {
    try {
        // 动态加载JSZip库
        if (!window.JSZip) {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js';
            document.head.appendChild(script);
            
            await new Promise((resolve, reject) => {
                script.onload = resolve;
                script.onerror = reject;
            });
        }
        
        const zip = new JSZip();
        const imageFolder = zip.folder("processed_images");
        
        showProgress(true);
        showMessage('正在创建压缩包...', 'info');
        
        const totalImages = selectedImages.length;
        let processedCount = 0;
        
        const currentCanvasIndex = currentImageIndex;
        
        for (let i = 0; i < selectedImages.length; i++) {
            try {
                updateProgress((processedCount / totalImages) * 100, `正在处理第 ${processedCount + 1} / ${totalImages} 张图片...`);
                
                // 切换到当前图片
                await loadImageToCanvas(i);
                
                // 等待画布渲染完成
                await new Promise(resolve => setTimeout(resolve, 100));

                // 🎯 获取高分辨率画布数据
                const dataURL = createHighResolutionCanvas();
                const base64Data = dataURL.split(',')[1];
                
                // 生成文件名
                const imageInfo = selectedImages[i];
                const originalName = imageInfo.name;
                const nameWithoutExt = originalName.replace(/\.[^/.]+$/, "");
                const fileName = `${nameWithoutExt}_processed.png`;
                
                // 添加到ZIP
                imageFolder.file(fileName, base64Data, { base64: true });
                
                console.log(`✅ 已添加到压缩包: ${fileName}`);
                
            } catch (error) {
                console.error(`❌ 处理失败 - ${selectedImages[i].name}:`, error);
            }
            
            processedCount++;
        }
        
        // 恢复到原来的图片
        if (currentCanvasIndex >= 0 && currentCanvasIndex < selectedImages.length) {
            await loadImageToCanvas(currentCanvasIndex);
        }
        
        updateProgress(90, '正在生成压缩包...');
        
        // 生成ZIP文件
        const content = await zip.generateAsync({ type: "blob" });
        
        // 创建下载链接
        const url = URL.createObjectURL(content);
        const a = document.createElement('a');
        a.href = url;
        a.download = `processed_images_${new Date().toISOString().slice(0, 10)}.zip`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        updateProgress(100, '下载完成');
        showMessage(`🎉 所有图片已打包下载！`, 'success');
        
        setTimeout(() => {
            showProgress(false);
        }, 2000);
        
    } catch (error) {
        console.error('创建压缩包时出错:', error);
        showProgress(false);
        showMessage('创建压缩包失败: ' + error.message, 'error');
    }
}

// 测试保存所有图片功能
function testSaveAllImages() {
    console.log('📍 ===== 测试保存所有图片功能 =====');
    console.log(`📍 当前图片数量: ${selectedImages.length}`);
    console.log(`📍 当前图片索引: ${currentImageIndex}`);
    
    if (selectedImages.length === 0) {
        console.log('❌ 没有图片可以保存');
        return;
    }
    
    console.log('📍 图片列表:');
    selectedImages.forEach((img, index) => {
        console.log(`  ${index + 1}. ${img.name} (选中: ${img.selected})`);
    });
    
    console.log('📍 浏览器支持检查:');
    console.log(`  - showDirectoryPicker: ${!!window.showDirectoryPicker}`);
    console.log(`  - JSZip: ${!!window.JSZip}`);
    
    console.log('📍 点击"保存所有图片"按钮开始测试...');
}

console.log('📍 注册测试函数: testSaveAllImages() - 测试保存所有图片功能');

// 测试二维码位置和尺寸的完整同步功能
function testQRCodeFullSync() {
    console.log('🔄 开始测试二维码位置和尺寸完整同步功能...');
    
    // 检查当前图片数量
    if (!selectedImages || selectedImages.length < 2) {
        console.log('❌ 需要至少2张图片才能测试同步功能');
        showMessage('需要至少2张图片才能测试同步功能', 'error');
        return;
    }
    
    // 检查当前画布的二维码
    const qrCodes = canvas.getObjects().filter(obj => obj.type === 'qrcode');
    if (qrCodes.length === 0) {
        console.log('❌ 当前画布没有二维码，无法测试同步功能');
        showMessage('当前画布没有二维码，无法测试同步功能', 'error');
        return;
    }
    
    const currentQR = qrCodes[0];
    
    console.log('📍 当前二维码状态:');
    console.log(`   位置: (${Math.round(currentQR.left)}, ${Math.round(currentQR.top)})`);
    console.log(`   尺寸: scaleX=${currentQR.scaleX.toFixed(2)}, scaleY=${currentQR.scaleY.toFixed(2)}`);
    console.log(`   全局位置: (${Math.round(globalQRCodePosition.left)}, ${Math.round(globalQRCodePosition.top)})`);
    console.log(`   全局尺寸: scaleX=${globalQRCodePosition.scaleX?.toFixed(2)}, scaleY=${globalQRCodePosition.scaleY?.toFixed(2)}`);
    
    // 强制同步位置和尺寸
    forceQRCodePositionSync();
    
    // 测试切换图片时的同步效果
    const originalIndex = currentImageIndex;
    let testIndex = originalIndex === 0 ? 1 : 0;
    
    console.log(`🔄 从图片 ${originalIndex} 切换到图片 ${testIndex} 进行测试...`);
    
    // 切换到测试图片
    loadImageToCanvas(testIndex);
    
    // 等待图片加载后检查二维码
    setTimeout(() => {
        const newQRCodes = canvas.getObjects().filter(obj => obj.type === 'qrcode');
        if (newQRCodes.length > 0) {
            const newQR = newQRCodes[0];
            console.log('📍 切换后的二维码状态:');
            console.log(`   位置: (${Math.round(newQR.left)}, ${Math.round(newQR.top)})`);
            console.log(`   尺寸: scaleX=${newQR.scaleX.toFixed(2)}, scaleY=${newQR.scaleY.toFixed(2)}`);
            
            // 检查是否同步成功
            const positionMatch = Math.abs(newQR.left - globalQRCodePosition.left) < 5 && 
                                Math.abs(newQR.top - globalQRCodePosition.top) < 5;
            const sizeMatch = Math.abs(newQR.scaleX - globalQRCodePosition.scaleX) < 0.01 && 
                            Math.abs(newQR.scaleY - globalQRCodePosition.scaleY) < 0.01;
            
            if (positionMatch && sizeMatch) {
                console.log('✅ 二维码位置和尺寸同步成功！');
                showMessage('二维码位置和尺寸同步测试通过！', 'success');
            } else {
                console.log('❌ 二维码同步失败:');
                console.log(`   位置匹配: ${positionMatch}`);
                console.log(`   尺寸匹配: ${sizeMatch}`);
                showMessage('二维码同步测试失败，请检查同步逻辑', 'error');
            }
        } else {
            console.log('❌ 切换后的图片没有二维码');
            showMessage('切换后的图片没有二维码', 'error');
        }
        
        // 切换回原图片
        setTimeout(() => {
            loadImageToCanvas(originalIndex);
            console.log(`🔄 已切换回原图片 ${originalIndex}`);
        }, 2000);
        
    }, 1000);
}

console.log('🔧 测试函数已注册: testQRCodeFullSync()');
console.log('📋 可在控制台调用以下测试函数:');
console.log('  - testQRCodeFullSync() - 测试二维码位置和尺寸完整同步');
console.log('  - testQRCodeSizeSync() - 测试二维码尺寸同步');
console.log('  - testForceSizeSync() - 强制尺寸同步测试');
console.log('  - testImageSwitching() - 测试图片切换时的同步');
console.log('  - testForceSync() - 强制位置同步测试');