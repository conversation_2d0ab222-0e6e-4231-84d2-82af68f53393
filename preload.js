const { contextBridge, ipc<PERSON>enderer } = require('electron');
const QRCode = require('qrcode');

// 在控制台输出调试信息
console.log('Preload脚本正在执行');

// 暴露给渲染进程的API
contextBridge.exposeInMainWorld('electronAPI', {
  // 选择图片
  selectImage: () => {
    console.log('调用selectImage API');
    return ipcRenderer.invoke('select-image');
  },
  
  // 保存图片
  saveImage: (buffer) => ipcRenderer.invoke('save-image', buffer),
  
  // 打印
  printImage: () => {
    ipcRenderer.send('print');
  }
});

// 暴露QRCode功能给渲染进程
contextBridge.exposeInMainWorld('QRCode', {
  toCanvas: (canvas, text, options, callback) => {
    return QRCode.toCanvas(canvas, text, options, callback);
  },
  toDataURL: (text, options, callback) => {
    return QRCode.toDataURL(text, options, callback);
  }
}); 