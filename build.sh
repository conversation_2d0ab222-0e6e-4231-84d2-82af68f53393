#!/bin/bash

echo "🚀 开始构建Go版本的图片处理应用..."

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo "❌ Go未安装，请先安装Go"
    exit 1
fi

# 检查是否已安装Wails
if ! command -v wails &> /dev/null; then
    echo "📦 Wails未安装，正在安装..."
    go install github.com/wailsapp/wails/v2/cmd/wails@latest
fi

# 初始化Wails项目
echo "🔧 初始化Wails项目..."
if [ ! -f "wails.json" ]; then
    wails init -n printphoto -t vanilla
    echo "✅ Wails项目初始化完成"
else
    echo "✅ Wails项目已存在"
fi

# 安装依赖
echo "📦 安装Go依赖..."
go mod tidy

# 开发模式运行
echo "🏃‍♂️ 启动开发模式..."
wails dev

echo "✅ 构建完成！" 