<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>图片处理与打印工具</title>
  <link rel="stylesheet" href="styles.css">
</head>
<body>
  <div class="container">
    <div class="sidebar">
      <h2>图片处理工具</h2>
      <div class="actions">
        <button id="select-image">选择图片</button>
        <div class="text-controls">
          <h3>添加文字</h3>
          <textarea id="text-content" placeholder="输入要添加的文字"></textarea>
          <div class="text-options">
            <select id="font-family">
              <option value="Arial">Arial</option>
              <option value="SimSun">宋体</option>
              <option value="Microsoft YaHei">微软雅黑</option>
              <option value="SimHei">黑体</option>
            </select>
            <input type="number" id="font-size" value="24" min="8" max="72">
            <input type="color" id="font-color" value="#000000">
          </div>
          <button id="add-text">添加文字</button>
        </div>
        <div class="upload-controls">
          <h3>上传到OSS</h3>
          <button id="upload-oss">上传到阿里云OSS</button>
          <div id="oss-result"></div>
        </div>
        <div class="qrcode-controls">
          <h3>生成二维码</h3>
          <input type="text" id="qrcode-text" placeholder="输入二维码内容">
          <button id="generate-qrcode">生成二维码</button>
        </div>
        <div class="print-controls">
          <button id="print-image">打印图片</button>
          <button id="save-image">保存图片</button>
        </div>
      </div>
    </div>
    <div class="editor">
      <div class="canvas-container">
        <canvas id="canvas"></canvas>
      </div>
    </div>
  </div>

  <!-- 引入依赖 -->
  <script src="node_modules/fabric/dist/fabric.js"></script>
  <!-- 使用CDN引入QRCode -->
  <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
  <script src="renderer.js"></script>
</body>
</html> 